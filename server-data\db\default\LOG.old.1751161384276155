2025/06/29-04:52:12.149655 4f58 RocksDB version: 8.3.0
2025/06/29-04:52:12.149763 4f58 DB SUMMARY
2025/06/29-04:52:12.149790 4f58 DB Session ID:  GK6JE3MPNI01ED5HE3RA
2025/06/29-04:52:12.151948 4f58 CURRENT file:  CURRENT
2025/06/29-04:52:12.151993 4f58 IDENTITY file:  IDENTITY
2025/06/29-04:52:12.152108 4f58 MANIFEST file:  MANIFEST-000227 size: 866 Bytes
2025/06/29-04:52:12.152131 4f58 SST files in E:\napoly\server-data\db\default dir, Total Num: 4, files: 000212.sst 000215.sst 000220.sst 000225.sst 
2025/06/29-04:52:12.152148 4f58 Write Ahead Log file in E:\napoly\server-data\db\default: 000226.log size: 1224 ; 
2025/06/29-04:52:12.152166 4f58                         Options.error_if_exists: 0
2025/06/29-04:52:12.152181 4f58                       Options.create_if_missing: 1
2025/06/29-04:52:12.152644 4f58                         Options.paranoid_checks: 1
2025/06/29-04:52:12.152656 4f58             Options.flush_verify_memtable_count: 1
2025/06/29-04:52:12.152660 4f58                               Options.track_and_verify_wals_in_manifest: 0
2025/06/29-04:52:12.152663 4f58        Options.verify_sst_unique_id_in_manifest: 1
2025/06/29-04:52:12.152666 4f58                                     Options.env: 000001AA2945EAC0
2025/06/29-04:52:12.152669 4f58                                      Options.fs: WinFS
2025/06/29-04:52:12.152672 4f58                                Options.info_log: 000001AA2DEFA6D0
2025/06/29-04:52:12.152675 4f58                Options.max_file_opening_threads: 16
2025/06/29-04:52:12.152678 4f58                              Options.statistics: 0000000000000000
2025/06/29-04:52:12.152681 4f58                               Options.use_fsync: 0
2025/06/29-04:52:12.152685 4f58                       Options.max_log_file_size: 0
2025/06/29-04:52:12.152690 4f58                  Options.max_manifest_file_size: 1073741824
2025/06/29-04:52:12.152694 4f58                   Options.log_file_time_to_roll: 0
2025/06/29-04:52:12.152698 4f58                       Options.keep_log_file_num: 10
2025/06/29-04:52:12.152703 4f58                    Options.recycle_log_file_num: 0
2025/06/29-04:52:12.152707 4f58                         Options.allow_fallocate: 1
2025/06/29-04:52:12.152710 4f58                        Options.allow_mmap_reads: 0
2025/06/29-04:52:12.152712 4f58                       Options.allow_mmap_writes: 0
2025/06/29-04:52:12.152715 4f58                        Options.use_direct_reads: 0
2025/06/29-04:52:12.152718 4f58                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/29-04:52:12.152721 4f58          Options.create_missing_column_families: 0
2025/06/29-04:52:12.152724 4f58                              Options.db_log_dir: 
2025/06/29-04:52:12.152727 4f58                                 Options.wal_dir: 
2025/06/29-04:52:12.152729 4f58                Options.table_cache_numshardbits: 6
2025/06/29-04:52:12.152732 4f58                         Options.WAL_ttl_seconds: 0
2025/06/29-04:52:12.152735 4f58                       Options.WAL_size_limit_MB: 0
2025/06/29-04:52:12.152738 4f58                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/29-04:52:12.152741 4f58             Options.manifest_preallocation_size: 4194304
2025/06/29-04:52:12.152744 4f58                     Options.is_fd_close_on_exec: 1
2025/06/29-04:52:12.152747 4f58                   Options.advise_random_on_open: 1
2025/06/29-04:52:12.152749 4f58                    Options.db_write_buffer_size: 0
2025/06/29-04:52:12.152752 4f58                    Options.write_buffer_manager: 000001AA2945E7F0
2025/06/29-04:52:12.152755 4f58         Options.access_hint_on_compaction_start: 1
2025/06/29-04:52:12.152758 4f58           Options.random_access_max_buffer_size: 1048576
2025/06/29-04:52:12.152760 4f58                      Options.use_adaptive_mutex: 0
2025/06/29-04:52:12.152763 4f58                            Options.rate_limiter: 0000000000000000
2025/06/29-04:52:12.152767 4f58     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/29-04:52:12.152770 4f58                       Options.wal_recovery_mode: 2
2025/06/29-04:52:12.152802 4f58                  Options.enable_thread_tracking: 0
2025/06/29-04:52:12.152808 4f58                  Options.enable_pipelined_write: 0
2025/06/29-04:52:12.152811 4f58                  Options.unordered_write: 0
2025/06/29-04:52:12.152814 4f58         Options.allow_concurrent_memtable_write: 1
2025/06/29-04:52:12.152817 4f58      Options.enable_write_thread_adaptive_yield: 1
2025/06/29-04:52:12.152820 4f58             Options.write_thread_max_yield_usec: 100
2025/06/29-04:52:12.152823 4f58            Options.write_thread_slow_yield_usec: 3
2025/06/29-04:52:12.152826 4f58                               Options.row_cache: None
2025/06/29-04:52:12.152829 4f58                              Options.wal_filter: None
2025/06/29-04:52:12.152832 4f58             Options.avoid_flush_during_recovery: 0
2025/06/29-04:52:12.152834 4f58             Options.allow_ingest_behind: 0
2025/06/29-04:52:12.152837 4f58             Options.two_write_queues: 0
2025/06/29-04:52:12.152840 4f58             Options.manual_wal_flush: 0
2025/06/29-04:52:12.152843 4f58             Options.wal_compression: 0
2025/06/29-04:52:12.152846 4f58             Options.atomic_flush: 0
2025/06/29-04:52:12.152848 4f58             Options.avoid_unnecessary_blocking_io: 0
2025/06/29-04:52:12.152851 4f58                 Options.persist_stats_to_disk: 0
2025/06/29-04:52:12.152854 4f58                 Options.write_dbid_to_manifest: 0
2025/06/29-04:52:12.152857 4f58                 Options.log_readahead_size: 0
2025/06/29-04:52:12.152859 4f58                 Options.file_checksum_gen_factory: Unknown
2025/06/29-04:52:12.152863 4f58                 Options.best_efforts_recovery: 0
2025/06/29-04:52:12.152865 4f58                Options.max_bgerror_resume_count: 2147483647
2025/06/29-04:52:12.152868 4f58            Options.bgerror_resume_retry_interval: 1000000
2025/06/29-04:52:12.152871 4f58             Options.allow_data_in_errors: 0
2025/06/29-04:52:12.152878 4f58             Options.db_host_id: __hostname__
2025/06/29-04:52:12.152884 4f58             Options.enforce_single_del_contracts: true
2025/06/29-04:52:12.152888 4f58             Options.max_background_jobs: 2
2025/06/29-04:52:12.152892 4f58             Options.max_background_compactions: -1
2025/06/29-04:52:12.152897 4f58             Options.max_subcompactions: 1
2025/06/29-04:52:12.152901 4f58             Options.avoid_flush_during_shutdown: 0
2025/06/29-04:52:12.152906 4f58           Options.writable_file_max_buffer_size: 1048576
2025/06/29-04:52:12.152910 4f58             Options.delayed_write_rate : 16777216
2025/06/29-04:52:12.152914 4f58             Options.max_total_wal_size: 0
2025/06/29-04:52:12.152918 4f58             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/29-04:52:12.152922 4f58                   Options.stats_dump_period_sec: 600
2025/06/29-04:52:12.152926 4f58                 Options.stats_persist_period_sec: 600
2025/06/29-04:52:12.152931 4f58                 Options.stats_history_buffer_size: 1048576
2025/06/29-04:52:12.152935 4f58                          Options.max_open_files: -1
2025/06/29-04:52:12.152940 4f58                          Options.bytes_per_sync: 0
2025/06/29-04:52:12.152944 4f58                      Options.wal_bytes_per_sync: 0
2025/06/29-04:52:12.152948 4f58                   Options.strict_bytes_per_sync: 0
2025/06/29-04:52:12.152952 4f58       Options.compaction_readahead_size: 0
2025/06/29-04:52:12.152956 4f58                  Options.max_background_flushes: -1
2025/06/29-04:52:12.152960 4f58 Compression algorithms supported:
2025/06/29-04:52:12.152970 4f58 	kZSTD supported: 0
2025/06/29-04:52:12.152975 4f58 	kSnappyCompression supported: 0
2025/06/29-04:52:12.152980 4f58 	kBZip2Compression supported: 0
2025/06/29-04:52:12.152984 4f58 	kZlibCompression supported: 1
2025/06/29-04:52:12.152989 4f58 	kLZ4Compression supported: 1
2025/06/29-04:52:12.152993 4f58 	kXpressCompression supported: 0
2025/06/29-04:52:12.152997 4f58 	kLZ4HCCompression supported: 1
2025/06/29-04:52:12.153002 4f58 	kZSTDNotFinalCompression supported: 0
2025/06/29-04:52:12.153038 4f58 Fast CRC32 supported: Not supported on x86
2025/06/29-04:52:12.153046 4f58 DMutex implementation: std::mutex
2025/06/29-04:52:12.154724 4f58 [db\version_set.cc:5791] Recovering from manifest file: E:\napoly\server-data\db\default/MANIFEST-000227
2025/06/29-04:52:12.155032 4f58 [db\column_family.cc:621] --------------- Options for column family [default]:
2025/06/29-04:52:12.155043 4f58               Options.comparator: leveldb.BytewiseComparator
2025/06/29-04:52:12.155049 4f58           Options.merge_operator: None
2025/06/29-04:52:12.155053 4f58        Options.compaction_filter: None
2025/06/29-04:52:12.155058 4f58        Options.compaction_filter_factory: None
2025/06/29-04:52:12.155062 4f58  Options.sst_partitioner_factory: None
2025/06/29-04:52:12.155067 4f58         Options.memtable_factory: SkipListFactory
2025/06/29-04:52:12.155268 4f58            Options.table_factory: BlockBasedTable
2025/06/29-04:52:12.155310 4f58            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001AA303CD330)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000001AA2945ECB0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-04:52:12.155316 4f58        Options.write_buffer_size: 67108864
2025/06/29-04:52:12.155320 4f58  Options.max_write_buffer_number: 2
2025/06/29-04:52:12.155324 4f58          Options.compression: LZ4
2025/06/29-04:52:12.155328 4f58                  Options.bottommost_compression: Disabled
2025/06/29-04:52:12.155332 4f58       Options.prefix_extractor: nullptr
2025/06/29-04:52:12.155335 4f58   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-04:52:12.155339 4f58             Options.num_levels: 7
2025/06/29-04:52:12.155343 4f58        Options.min_write_buffer_number_to_merge: 1
2025/06/29-04:52:12.155347 4f58     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-04:52:12.155350 4f58     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-04:52:12.155355 4f58            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-04:52:12.155359 4f58                  Options.bottommost_compression_opts.level: 32767
2025/06/29-04:52:12.155363 4f58               Options.bottommost_compression_opts.strategy: 0
2025/06/29-04:52:12.155367 4f58         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-04:52:12.155371 4f58         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-04:52:12.155375 4f58         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-04:52:12.155379 4f58                  Options.bottommost_compression_opts.enabled: false
2025/06/29-04:52:12.155384 4f58         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-04:52:12.155388 4f58         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-04:52:12.155393 4f58            Options.compression_opts.window_bits: -14
2025/06/29-04:52:12.155397 4f58                  Options.compression_opts.level: 32767
2025/06/29-04:52:12.155402 4f58               Options.compression_opts.strategy: 0
2025/06/29-04:52:12.155450 4f58         Options.compression_opts.max_dict_bytes: 0
2025/06/29-04:52:12.155458 4f58         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-04:52:12.155462 4f58         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-04:52:12.155467 4f58         Options.compression_opts.parallel_threads: 1
2025/06/29-04:52:12.155470 4f58                  Options.compression_opts.enabled: false
2025/06/29-04:52:12.155474 4f58         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-04:52:12.155479 4f58      Options.level0_file_num_compaction_trigger: 4
2025/06/29-04:52:12.155484 4f58          Options.level0_slowdown_writes_trigger: 20
2025/06/29-04:52:12.155488 4f58              Options.level0_stop_writes_trigger: 36
2025/06/29-04:52:12.155491 4f58                   Options.target_file_size_base: 67108864
2025/06/29-04:52:12.155494 4f58             Options.target_file_size_multiplier: 1
2025/06/29-04:52:12.155497 4f58                Options.max_bytes_for_level_base: 268435456
2025/06/29-04:52:12.155500 4f58 Options.level_compaction_dynamic_level_bytes: 0
2025/06/29-04:52:12.155503 4f58          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-04:52:12.155507 4f58 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-04:52:12.155510 4f58 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-04:52:12.155514 4f58 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-04:52:12.155518 4f58 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-04:52:12.155522 4f58 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-04:52:12.155526 4f58 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-04:52:12.155541 4f58 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-04:52:12.155547 4f58       Options.max_sequential_skip_in_iterations: 8
2025/06/29-04:52:12.155551 4f58                    Options.max_compaction_bytes: 1677721600
2025/06/29-04:52:12.155556 4f58   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-04:52:12.155561 4f58                        Options.arena_block_size: 1048576
2025/06/29-04:52:12.155565 4f58   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-04:52:12.155570 4f58   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-04:52:12.155574 4f58                Options.disable_auto_compactions: 0
2025/06/29-04:52:12.155583 4f58                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-04:52:12.155589 4f58                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-04:52:12.155593 4f58 Options.compaction_options_universal.size_ratio: 1
2025/06/29-04:52:12.155598 4f58 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-04:52:12.155602 4f58 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-04:52:12.155607 4f58 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-04:52:12.155612 4f58 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-04:52:12.155617 4f58 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-04:52:12.155622 4f58 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-04:52:12.155627 4f58 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-04:52:12.155634 4f58                   Options.table_properties_collectors: 
2025/06/29-04:52:12.155638 4f58                   Options.inplace_update_support: 0
2025/06/29-04:52:12.155643 4f58                 Options.inplace_update_num_locks: 10000
2025/06/29-04:52:12.155647 4f58               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-04:52:12.155652 4f58               Options.memtable_whole_key_filtering: 0
2025/06/29-04:52:12.155656 4f58   Options.memtable_huge_page_size: 0
2025/06/29-04:52:12.155661 4f58                           Options.bloom_locality: 0
2025/06/29-04:52:12.155665 4f58                    Options.max_successive_merges: 0
2025/06/29-04:52:12.155670 4f58                Options.optimize_filters_for_hits: 0
2025/06/29-04:52:12.155677 4f58                Options.paranoid_file_checks: 0
2025/06/29-04:52:12.155682 4f58                Options.force_consistency_checks: 1
2025/06/29-04:52:12.155686 4f58                Options.report_bg_io_stats: 0
2025/06/29-04:52:12.155691 4f58                               Options.ttl: 2592000
2025/06/29-04:52:12.155695 4f58          Options.periodic_compaction_seconds: 0
2025/06/29-04:52:12.155700 4f58  Options.preclude_last_level_data_seconds: 0
2025/06/29-04:52:12.155704 4f58    Options.preserve_internal_time_seconds: 0
2025/06/29-04:52:12.155708 4f58                       Options.enable_blob_files: false
2025/06/29-04:52:12.155713 4f58                           Options.min_blob_size: 0
2025/06/29-04:52:12.155717 4f58                          Options.blob_file_size: 268435456
2025/06/29-04:52:12.155721 4f58                   Options.blob_compression_type: NoCompression
2025/06/29-04:52:12.155726 4f58          Options.enable_blob_garbage_collection: false
2025/06/29-04:52:12.155730 4f58      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-04:52:12.155736 4f58 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-04:52:12.155741 4f58          Options.blob_compaction_readahead_size: 0
2025/06/29-04:52:12.155746 4f58                Options.blob_file_starting_level: 0
2025/06/29-04:52:12.155750 4f58 Options.experimental_mempurge_threshold: 0.000000
2025/06/29-04:52:12.159367 4f58 [db\version_set.cc:5842] Recovered from manifest file:E:\napoly\server-data\db\default/MANIFEST-000227 succeeded,manifest_file_number is 227, next_file_number is 229, last_sequence is 669, log_number is 222,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 222
2025/06/29-04:52:12.159394 4f58 [db\version_set.cc:5851] Column family [default] (ID 0), log number is 222
2025/06/29-04:52:12.159753 4f58 [db\db_impl\db_impl_open.cc:636] DB ID: 1ac3d8dc-4830-11f0-b4ab-244bfe56a7b1
2025/06/29-04:52:12.163515 4f58 EVENT_LOG_v1 {"time_micros": 1751158332163502, "job": 1, "event": "recovery_started", "wal_files": [226]}
2025/06/29-04:52:12.163544 4f58 [db\db_impl\db_impl_open.cc:1131] Recovering log #226 mode 2
2025/06/29-04:52:12.201034 4f58 EVENT_LOG_v1 {"time_micros": 1751158332200989, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 230, "file_size": 1375, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 670, "largest_seqno": 683, "table_properties": {"data_size": 347, "index_size": 74, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 951, "raw_average_key_size": 67, "raw_value_size": 77, "raw_average_value_size": 5, "num_data_blocks": 1, "num_entries": 14, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "LZ4", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751158332, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "1ac3d8dc-4830-11f0-b4ab-244bfe56a7b1", "db_session_id": "GK6JE3MPNI01ED5HE3RA", "orig_file_number": 230, "seqno_to_time_mapping": "N/A"}}
2025/06/29-04:52:12.212341 4f58 EVENT_LOG_v1 {"time_micros": 1751158332212326, "job": 1, "event": "recovery_finished"}
2025/06/29-04:52:12.212936 4f58 [db\version_set.cc:5304] Creating manifest 232
2025/06/29-04:52:12.284160 4f58 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000226.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-04:52:12.284192 4f58 [db\db_impl\db_impl_files.cc:654] [JOB 2] Delete info log file E:\napoly\server-data\db\default//LOG.old.1751148094359378
2025/06/29-04:52:12.284493 4f58 [db\db_impl\db_impl_open.cc:2085] SstFileManager instance 000001AA308CCD70
2025/06/29-04:52:12.284678 6f14 [db\compaction\compaction_job.cc:1992] [default] [JOB 3] Compacting 4@0 + 1@1 files to L1, score 1.00
2025/06/29-04:52:12.284701 6f14 [db\compaction\compaction_job.cc:1996] [default]: Compaction start summary: Base version 2 Base level 0, inputs: [230(1375B) 225(1375B) 220(1375B) 215(1375B)], [212(1384B)]
2025/06/29-04:52:12.284738 6f14 EVENT_LOG_v1 {"time_micros": 1751158332284716, "job": 3, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [230, 225, 220, 215], "files_L1": [212], "score": 1, "input_data_size": 6884, "oldest_snapshot_seqno": -1}
2025/06/29-04:52:12.286463 4f58 DB pointer 000001AA30022100
2025/06/29-04:52:12.287071 5dc [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-04:52:12.287088 5dc [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      4/4    5.37 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.037       0      0       0.0       0.0
  L1      1/1    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      5/5    6.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.037       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.037       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.04              0.00         1    0.037       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000001AA2945ECB0#28572 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 1 last_copies: 0 last_secs: 6.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/29-04:52:12.313008 6f14 [db\compaction\compaction_job.cc:1595] [default] [JOB 3] Generated table #235: 15 keys, 1384 bytes, temperature: kUnknown
2025/06/29-04:52:12.313123 6f14 EVENT_LOG_v1 {"time_micros": 1751158332313065, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 235, "file_size": 1384, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 0, "largest_seqno": 0, "table_properties": {"data_size": 333, "index_size": 74, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 981, "raw_average_key_size": 65, "raw_value_size": 78, "raw_average_value_size": 5, "num_data_blocks": 1, "num_entries": 15, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "LZ4", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1749804246, "oldest_key_time": 0, "file_creation_time": 1751158332, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "1ac3d8dc-4830-11f0-b4ab-244bfe56a7b1", "db_session_id": "GK6JE3MPNI01ED5HE3RA", "orig_file_number": 235, "seqno_to_time_mapping": "N/A"}}
2025/06/29-04:52:12.354994 6f14 (Original Log Time 2025/06/29-04:52:12.330470) [db\compaction\compaction_job.cc:1667] [default] [JOB 3] Compacted 4@0 + 1@1 files to L1 => 1384 bytes
2025/06/29-04:52:12.355032 6f14 (Original Log Time 2025/06/29-04:52:12.354844) [db\compaction\compaction_job.cc:888] [default] compacted to: files[0 1 0 0 0 0 0] max score 0.00, MB/sec: 0.2 rd, 0.0 wr, level 1, files in(4, 1) out(1 +0 blob) MB in(0.0, 0.0 +0.0 blob) out(0.0 +0.0 blob), read-write-amplify(1.5) write-amplify(0.3) OK, records in: 71, records dropped: 56 output_compression: LZ4
2025/06/29-04:52:12.355042 6f14 (Original Log Time 2025/06/29-04:52:12.354917) EVENT_LOG_v1 {"time_micros": 1751158332354874, "job": 3, "event": "compaction_finished", "compaction_time_micros": 28586, "compaction_time_cpu_micros": 0, "output_level": 1, "num_output_files": 1, "total_output_size": 1384, "num_input_records": 71, "num_output_records": 15, "num_subcompactions": 1, "output_compression": "LZ4", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 1, 0, 0, 0, 0, 0]}
2025/06/29-04:52:12.355587 6f14 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000230.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-04:52:12.355628 6f14 EVENT_LOG_v1 {"time_micros": 1751158332355618, "job": 3, "event": "table_file_deletion", "file_number": 230}
2025/06/29-04:52:12.355925 6f14 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000225.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-04:52:12.355951 6f14 EVENT_LOG_v1 {"time_micros": 1751158332355944, "job": 3, "event": "table_file_deletion", "file_number": 225}
2025/06/29-04:52:12.356243 6f14 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000220.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-04:52:12.356270 6f14 EVENT_LOG_v1 {"time_micros": 1751158332356265, "job": 3, "event": "table_file_deletion", "file_number": 220}
2025/06/29-04:52:12.356518 6f14 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000215.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-04:52:12.356537 6f14 EVENT_LOG_v1 {"time_micros": 1751158332356532, "job": 3, "event": "table_file_deletion", "file_number": 215}
2025/06/29-04:52:12.356759 6f14 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000212.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-04:52:12.356777 6f14 EVENT_LOG_v1 {"time_micros": 1751158332356772, "job": 3, "event": "table_file_deletion", "file_number": 212}
2025/06/29-05:02:12.288909 5dc [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-05:02:12.288935 5dc [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 600.1 total, 600.0 interval
Cumulative writes: 28 writes, 28 keys, 28 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 28 writes, 28 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 27 writes, 27 keys, 27 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 28 writes, 28 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.037       0      0       0.0       0.0
  L1      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.3      0.2      0.0      0.03              0.00         1    0.029      71     56       0.0       0.0
 Sum      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.1      0.0      0.07              0.00         2    0.033      71     56       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0 1384.0      0.2      0.0      0.03              0.00         1    0.029      71     56       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.2      0.0      0.03              0.00         1    0.029      71     56       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.04              0.00         1    0.037       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 600.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000001AA2945ECB0#28572 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 2 last_copies: 0 last_secs: 8.3e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/29-05:12:12.290744 5dc [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-05:12:12.290784 5dc [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 1200.1 total, 600.0 interval
Cumulative writes: 28 writes, 28 keys, 28 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 28 writes, 28 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.037       0      0       0.0       0.0
  L1      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.3      0.2      0.0      0.03              0.00         1    0.029      71     56       0.0       0.0
 Sum      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.1      0.0      0.07              0.00         2    0.033      71     56       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.2      0.0      0.03              0.00         1    0.029      71     56       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.04              0.00         1    0.037       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1200.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000001AA2945ECB0#28572 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 3 last_copies: 0 last_secs: 6e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
