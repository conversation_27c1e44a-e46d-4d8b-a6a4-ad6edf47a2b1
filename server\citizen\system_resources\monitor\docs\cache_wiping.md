### Cache Wiping

This feature was requested many times and in the end we decided against putting it into txAdmin for the following reasons:
- Its just not necessary with the newest builds of FiveM anymore.
- It can be dangerous considering that not configuring it correctly might end up recursively wiping out important files.

More info: https://forum.fivem.net/t/why-people-delete-the-server-cache-folder/573851
