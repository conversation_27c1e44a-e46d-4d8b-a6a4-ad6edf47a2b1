2025/06/29-16:41:54.316956 19e0 RocksDB version: 8.3.0
2025/06/29-16:41:54.317013 19e0 DB SUMMARY
2025/06/29-16:41:54.317027 19e0 DB Session ID:  QQIBIW7W7FD8MK65DACE
2025/06/29-16:41:54.318144 19e0 CURRENT file:  CURRENT
2025/06/29-16:41:54.318160 19e0 IDENTITY file:  IDENTITY
2025/06/29-16:41:54.318243 19e0 MANIFEST file:  MANIFEST-000240 size: 496 Bytes
2025/06/29-16:41:54.318257 19e0 SST files in E:\napoly\server-data\db\default dir, Total Num: 2, files: 000235.sst 000238.sst 
2025/06/29-16:41:54.318267 19e0 Write Ahead Log file in E:\napoly\server-data\db\default: 000239.log size: 1224 ; 
2025/06/29-16:41:54.318277 19e0                         Options.error_if_exists: 0
2025/06/29-16:41:54.318287 19e0                       Options.create_if_missing: 1
2025/06/29-16:41:54.318675 19e0                         Options.paranoid_checks: 1
2025/06/29-16:41:54.318685 19e0             Options.flush_verify_memtable_count: 1
2025/06/29-16:41:54.318688 19e0                               Options.track_and_verify_wals_in_manifest: 0
2025/06/29-16:41:54.318691 19e0        Options.verify_sst_unique_id_in_manifest: 1
2025/06/29-16:41:54.318694 19e0                                     Options.env: 000002703A4F9F30
2025/06/29-16:41:54.318698 19e0                                      Options.fs: WinFS
2025/06/29-16:41:54.318701 19e0                                Options.info_log: 000002705C16D250
2025/06/29-16:41:54.318703 19e0                Options.max_file_opening_threads: 16
2025/06/29-16:41:54.318706 19e0                              Options.statistics: 0000000000000000
2025/06/29-16:41:54.318709 19e0                               Options.use_fsync: 0
2025/06/29-16:41:54.318712 19e0                       Options.max_log_file_size: 0
2025/06/29-16:41:54.318715 19e0                  Options.max_manifest_file_size: 1073741824
2025/06/29-16:41:54.318718 19e0                   Options.log_file_time_to_roll: 0
2025/06/29-16:41:54.318721 19e0                       Options.keep_log_file_num: 10
2025/06/29-16:41:54.318723 19e0                    Options.recycle_log_file_num: 0
2025/06/29-16:41:54.318726 19e0                         Options.allow_fallocate: 1
2025/06/29-16:41:54.318729 19e0                        Options.allow_mmap_reads: 0
2025/06/29-16:41:54.318732 19e0                       Options.allow_mmap_writes: 0
2025/06/29-16:41:54.318735 19e0                        Options.use_direct_reads: 0
2025/06/29-16:41:54.318737 19e0                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/29-16:41:54.318740 19e0          Options.create_missing_column_families: 0
2025/06/29-16:41:54.318743 19e0                              Options.db_log_dir: 
2025/06/29-16:41:54.318746 19e0                                 Options.wal_dir: 
2025/06/29-16:41:54.318749 19e0                Options.table_cache_numshardbits: 6
2025/06/29-16:41:54.318752 19e0                         Options.WAL_ttl_seconds: 0
2025/06/29-16:41:54.318755 19e0                       Options.WAL_size_limit_MB: 0
2025/06/29-16:41:54.318757 19e0                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/29-16:41:54.318760 19e0             Options.manifest_preallocation_size: 4194304
2025/06/29-16:41:54.318763 19e0                     Options.is_fd_close_on_exec: 1
2025/06/29-16:41:54.318766 19e0                   Options.advise_random_on_open: 1
2025/06/29-16:41:54.318769 19e0                    Options.db_write_buffer_size: 0
2025/06/29-16:41:54.318772 19e0                    Options.write_buffer_manager: 000002703B829CF0
2025/06/29-16:41:54.318775 19e0         Options.access_hint_on_compaction_start: 1
2025/06/29-16:41:54.318777 19e0           Options.random_access_max_buffer_size: 1048576
2025/06/29-16:41:54.318780 19e0                      Options.use_adaptive_mutex: 0
2025/06/29-16:41:54.318783 19e0                            Options.rate_limiter: 0000000000000000
2025/06/29-16:41:54.318786 19e0     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/29-16:41:54.318789 19e0                       Options.wal_recovery_mode: 2
2025/06/29-16:41:54.318792 19e0                  Options.enable_thread_tracking: 0
2025/06/29-16:41:54.318820 19e0                  Options.enable_pipelined_write: 0
2025/06/29-16:41:54.318826 19e0                  Options.unordered_write: 0
2025/06/29-16:41:54.318829 19e0         Options.allow_concurrent_memtable_write: 1
2025/06/29-16:41:54.318832 19e0      Options.enable_write_thread_adaptive_yield: 1
2025/06/29-16:41:54.318835 19e0             Options.write_thread_max_yield_usec: 100
2025/06/29-16:41:54.318838 19e0            Options.write_thread_slow_yield_usec: 3
2025/06/29-16:41:54.318840 19e0                               Options.row_cache: None
2025/06/29-16:41:54.318843 19e0                              Options.wal_filter: None
2025/06/29-16:41:54.318846 19e0             Options.avoid_flush_during_recovery: 0
2025/06/29-16:41:54.318849 19e0             Options.allow_ingest_behind: 0
2025/06/29-16:41:54.318852 19e0             Options.two_write_queues: 0
2025/06/29-16:41:54.318854 19e0             Options.manual_wal_flush: 0
2025/06/29-16:41:54.318857 19e0             Options.wal_compression: 0
2025/06/29-16:41:54.318860 19e0             Options.atomic_flush: 0
2025/06/29-16:41:54.318863 19e0             Options.avoid_unnecessary_blocking_io: 0
2025/06/29-16:41:54.318866 19e0                 Options.persist_stats_to_disk: 0
2025/06/29-16:41:54.318869 19e0                 Options.write_dbid_to_manifest: 0
2025/06/29-16:41:54.318871 19e0                 Options.log_readahead_size: 0
2025/06/29-16:41:54.318874 19e0                 Options.file_checksum_gen_factory: Unknown
2025/06/29-16:41:54.318877 19e0                 Options.best_efforts_recovery: 0
2025/06/29-16:41:54.318880 19e0                Options.max_bgerror_resume_count: 2147483647
2025/06/29-16:41:54.318883 19e0            Options.bgerror_resume_retry_interval: 1000000
2025/06/29-16:41:54.318886 19e0             Options.allow_data_in_errors: 0
2025/06/29-16:41:54.318888 19e0             Options.db_host_id: __hostname__
2025/06/29-16:41:54.318891 19e0             Options.enforce_single_del_contracts: true
2025/06/29-16:41:54.318894 19e0             Options.max_background_jobs: 2
2025/06/29-16:41:54.318897 19e0             Options.max_background_compactions: -1
2025/06/29-16:41:54.318900 19e0             Options.max_subcompactions: 1
2025/06/29-16:41:54.318903 19e0             Options.avoid_flush_during_shutdown: 0
2025/06/29-16:41:54.318906 19e0           Options.writable_file_max_buffer_size: 1048576
2025/06/29-16:41:54.318908 19e0             Options.delayed_write_rate : 16777216
2025/06/29-16:41:54.318911 19e0             Options.max_total_wal_size: 0
2025/06/29-16:41:54.318914 19e0             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/29-16:41:54.318917 19e0                   Options.stats_dump_period_sec: 600
2025/06/29-16:41:54.318920 19e0                 Options.stats_persist_period_sec: 600
2025/06/29-16:41:54.318922 19e0                 Options.stats_history_buffer_size: 1048576
2025/06/29-16:41:54.318925 19e0                          Options.max_open_files: -1
2025/06/29-16:41:54.318928 19e0                          Options.bytes_per_sync: 0
2025/06/29-16:41:54.318931 19e0                      Options.wal_bytes_per_sync: 0
2025/06/29-16:41:54.318934 19e0                   Options.strict_bytes_per_sync: 0
2025/06/29-16:41:54.318937 19e0       Options.compaction_readahead_size: 0
2025/06/29-16:41:54.318940 19e0                  Options.max_background_flushes: -1
2025/06/29-16:41:54.318942 19e0 Compression algorithms supported:
2025/06/29-16:41:54.318948 19e0 	kZSTD supported: 0
2025/06/29-16:41:54.318951 19e0 	kSnappyCompression supported: 0
2025/06/29-16:41:54.318954 19e0 	kBZip2Compression supported: 0
2025/06/29-16:41:54.318957 19e0 	kZlibCompression supported: 1
2025/06/29-16:41:54.318960 19e0 	kLZ4Compression supported: 1
2025/06/29-16:41:54.318963 19e0 	kXpressCompression supported: 0
2025/06/29-16:41:54.318966 19e0 	kLZ4HCCompression supported: 1
2025/06/29-16:41:54.318969 19e0 	kZSTDNotFinalCompression supported: 0
2025/06/29-16:41:54.318975 19e0 Fast CRC32 supported: Not supported on x86
2025/06/29-16:41:54.318996 19e0 DMutex implementation: std::mutex
2025/06/29-16:41:54.326317 19e0 [db\version_set.cc:5791] Recovering from manifest file: E:\napoly\server-data\db\default/MANIFEST-000240
2025/06/29-16:41:54.340485 19e0 [db\column_family.cc:621] --------------- Options for column family [default]:
2025/06/29-16:41:54.340502 19e0               Options.comparator: leveldb.BytewiseComparator
2025/06/29-16:41:54.340506 19e0           Options.merge_operator: None
2025/06/29-16:41:54.340509 19e0        Options.compaction_filter: None
2025/06/29-16:41:54.340512 19e0        Options.compaction_filter_factory: None
2025/06/29-16:41:54.340515 19e0  Options.sst_partitioner_factory: None
2025/06/29-16:41:54.340518 19e0         Options.memtable_factory: SkipListFactory
2025/06/29-16:41:54.340521 19e0            Options.table_factory: BlockBasedTable
2025/06/29-16:41:54.340555 19e0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002705C0C2CB0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000002703A4F87D0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-16:41:54.340559 19e0        Options.write_buffer_size: 67108864
2025/06/29-16:41:54.340562 19e0  Options.max_write_buffer_number: 2
2025/06/29-16:41:54.340565 19e0          Options.compression: LZ4
2025/06/29-16:41:54.340568 19e0                  Options.bottommost_compression: Disabled
2025/06/29-16:41:54.340571 19e0       Options.prefix_extractor: nullptr
2025/06/29-16:41:54.340574 19e0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-16:41:54.340577 19e0             Options.num_levels: 7
2025/06/29-16:41:54.340580 19e0        Options.min_write_buffer_number_to_merge: 1
2025/06/29-16:41:54.340582 19e0     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-16:41:54.340585 19e0     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-16:41:54.340588 19e0            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-16:41:54.340591 19e0                  Options.bottommost_compression_opts.level: 32767
2025/06/29-16:41:54.340594 19e0               Options.bottommost_compression_opts.strategy: 0
2025/06/29-16:41:54.340597 19e0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-16:41:54.340600 19e0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-16:41:54.340602 19e0         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-16:41:54.340605 19e0                  Options.bottommost_compression_opts.enabled: false
2025/06/29-16:41:54.340608 19e0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-16:41:54.340611 19e0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-16:41:54.340614 19e0            Options.compression_opts.window_bits: -14
2025/06/29-16:41:54.340617 19e0                  Options.compression_opts.level: 32767
2025/06/29-16:41:54.340620 19e0               Options.compression_opts.strategy: 0
2025/06/29-16:41:54.340623 19e0         Options.compression_opts.max_dict_bytes: 0
2025/06/29-16:41:54.340628 19e0         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-16:41:54.340633 19e0         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-16:41:54.340636 19e0         Options.compression_opts.parallel_threads: 1
2025/06/29-16:41:54.340638 19e0                  Options.compression_opts.enabled: false
2025/06/29-16:41:54.340641 19e0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-16:41:54.340644 19e0      Options.level0_file_num_compaction_trigger: 4
2025/06/29-16:41:54.340647 19e0          Options.level0_slowdown_writes_trigger: 20
2025/06/29-16:41:54.340650 19e0              Options.level0_stop_writes_trigger: 36
2025/06/29-16:41:54.340653 19e0                   Options.target_file_size_base: 67108864
2025/06/29-16:41:54.340655 19e0             Options.target_file_size_multiplier: 1
2025/06/29-16:41:54.340852 19e0                Options.max_bytes_for_level_base: 268435456
2025/06/29-16:41:54.340857 19e0 Options.level_compaction_dynamic_level_bytes: 0
2025/06/29-16:41:54.340860 19e0          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-16:41:54.340864 19e0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-16:41:54.340867 19e0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-16:41:54.340870 19e0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-16:41:54.340873 19e0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-16:41:54.340875 19e0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-16:41:54.340878 19e0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-16:41:54.340881 19e0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-16:41:54.340884 19e0       Options.max_sequential_skip_in_iterations: 8
2025/06/29-16:41:54.340887 19e0                    Options.max_compaction_bytes: 1677721600
2025/06/29-16:41:54.340890 19e0   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-16:41:54.340892 19e0                        Options.arena_block_size: 1048576
2025/06/29-16:41:54.340895 19e0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-16:41:54.340898 19e0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-16:41:54.340901 19e0                Options.disable_auto_compactions: 0
2025/06/29-16:41:54.340907 19e0                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-16:41:54.340912 19e0                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-16:41:54.340915 19e0 Options.compaction_options_universal.size_ratio: 1
2025/06/29-16:41:54.340917 19e0 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-16:41:54.340920 19e0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-16:41:54.340923 19e0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-16:41:54.340926 19e0 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-16:41:54.340929 19e0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-16:41:54.340932 19e0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-16:41:54.340935 19e0 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-16:41:54.340940 19e0                   Options.table_properties_collectors: 
2025/06/29-16:41:54.340943 19e0                   Options.inplace_update_support: 0
2025/06/29-16:41:54.340945 19e0                 Options.inplace_update_num_locks: 10000
2025/06/29-16:41:54.340948 19e0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-16:41:54.340951 19e0               Options.memtable_whole_key_filtering: 0
2025/06/29-16:41:54.340954 19e0   Options.memtable_huge_page_size: 0
2025/06/29-16:41:54.340957 19e0                           Options.bloom_locality: 0
2025/06/29-16:41:54.340960 19e0                    Options.max_successive_merges: 0
2025/06/29-16:41:54.340963 19e0                Options.optimize_filters_for_hits: 0
2025/06/29-16:41:54.341036 19e0                Options.paranoid_file_checks: 0
2025/06/29-16:41:54.341045 19e0                Options.force_consistency_checks: 1
2025/06/29-16:41:54.341049 19e0                Options.report_bg_io_stats: 0
2025/06/29-16:41:54.341053 19e0                               Options.ttl: 2592000
2025/06/29-16:41:54.341057 19e0          Options.periodic_compaction_seconds: 0
2025/06/29-16:41:54.341061 19e0  Options.preclude_last_level_data_seconds: 0
2025/06/29-16:41:54.341065 19e0    Options.preserve_internal_time_seconds: 0
2025/06/29-16:41:54.341068 19e0                       Options.enable_blob_files: false
2025/06/29-16:41:54.341072 19e0                           Options.min_blob_size: 0
2025/06/29-16:41:54.341076 19e0                          Options.blob_file_size: 268435456
2025/06/29-16:41:54.341080 19e0                   Options.blob_compression_type: NoCompression
2025/06/29-16:41:54.341084 19e0          Options.enable_blob_garbage_collection: false
2025/06/29-16:41:54.341088 19e0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-16:41:54.341094 19e0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-16:41:54.341098 19e0          Options.blob_compaction_readahead_size: 0
2025/06/29-16:41:54.341102 19e0                Options.blob_file_starting_level: 0
2025/06/29-16:41:54.341106 19e0 Options.experimental_mempurge_threshold: 0.000000
2025/06/29-16:41:54.373699 19e0 [db\version_set.cc:5842] Recovered from manifest file:E:\napoly\server-data\db\default/MANIFEST-000240 succeeded,manifest_file_number is 240, next_file_number is 242, last_sequence is 711, log_number is 232,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 232
2025/06/29-16:41:54.373716 19e0 [db\version_set.cc:5851] Column family [default] (ID 0), log number is 232
2025/06/29-16:41:54.380358 19e0 [db\db_impl\db_impl_open.cc:636] DB ID: 1ac3d8dc-4830-11f0-b4ab-244bfe56a7b1
2025/06/29-16:41:54.382626 19e0 EVENT_LOG_v1 {"time_micros": 1751200914382619, "job": 1, "event": "recovery_started", "wal_files": [239]}
2025/06/29-16:41:54.382636 19e0 [db\db_impl\db_impl_open.cc:1131] Recovering log #239 mode 2
2025/06/29-16:41:54.445787 19e0 EVENT_LOG_v1 {"time_micros": 1751200914445751, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 243, "file_size": 1375, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 712, "largest_seqno": 725, "table_properties": {"data_size": 347, "index_size": 74, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 951, "raw_average_key_size": 67, "raw_value_size": 77, "raw_average_value_size": 5, "num_data_blocks": 1, "num_entries": 14, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "LZ4", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751200914, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "1ac3d8dc-4830-11f0-b4ab-244bfe56a7b1", "db_session_id": "QQIBIW7W7FD8MK65DACE", "orig_file_number": 243, "seqno_to_time_mapping": "N/A"}}
2025/06/29-16:41:54.453716 19e0 EVENT_LOG_v1 {"time_micros": 1751200914453707, "job": 1, "event": "recovery_finished"}
2025/06/29-16:41:54.454191 19e0 [db\version_set.cc:5304] Creating manifest 245
2025/06/29-16:41:54.524301 19e0 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000239.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-16:41:54.524329 19e0 [db\db_impl\db_impl_files.cc:654] [JOB 2] Delete info log file E:\napoly\server-data\db\default//LOG.old.1751149720263921
2025/06/29-16:41:54.524568 19e0 [db\db_impl\db_impl_open.cc:2085] SstFileManager instance 0000027059297ED0
2025/06/29-16:41:54.525660 19e0 DB pointer 0000027050BFB040
2025/06/29-16:41:54.526127 8c0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-16:41:54.526139 8c0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 0.2 total, 0.2 interval
Cumulative writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.69 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.03              0.00         1    0.034       0      0       0.0       0.0
  L1      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    4.04 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.03              0.00         1    0.034       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.03              0.00         1    0.034       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.03              0.00         1    0.034       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.2 total, 0.2 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002703A4F87D0#5808 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 1 last_copies: 0 last_secs: 5.8e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/29-16:51:54.527385 8c0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-16:51:54.527414 8c0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 600.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 13 writes, 13 keys, 13 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.69 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.03              0.00         1    0.034       0      0       0.0       0.0
  L1      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    4.04 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.03              0.00         1    0.034       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.03              0.00         1    0.034       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 600.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002703A4F87D0#5808 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 2 last_copies: 0 last_secs: 6.3e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/29-17:01:54.528373 8c0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-17:01:54.528458 8c0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 1200.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.69 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.03              0.00         1    0.034       0      0       0.0       0.0
  L1      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    4.04 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.03              0.00         1    0.034       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.03              0.00         1    0.034       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1200.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002703A4F87D0#5808 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 3 last_copies: 0 last_secs: 6.8e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
