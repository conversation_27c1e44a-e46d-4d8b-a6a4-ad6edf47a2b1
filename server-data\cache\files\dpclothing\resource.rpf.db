[{"n": "E:/napoly/server-data/resources//[standalone]/dpclothing/@es_extended/imports.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "E:/napoly/server-data/resources//[standalone]/dpclothing/Client/Clothing.lua", "mt": 1702509108, "s": 12791, "i": "duwBAAAABQAAAAAAAAAAAA=="}, {"n": "E:/napoly/server-data/resources//[standalone]/dpclothing/Client/Config.lua", "mt": 1702509108, "s": 6265, "i": "d+wBAAAABQAAAAAAAAAAAA=="}, {"n": "E:/napoly/server-data/resources//[standalone]/dpclothing/Client/Functions.lua", "mt": 1702509108, "s": 5476, "i": "eOwBAAAABQAAAAAAAAAAAA=="}, {"n": "E:/napoly/server-data/resources//[standalone]/dpclothing/Client/GUI.lua", "mt": 1702509108, "s": 10327, "i": "eewBAAAABQAAAAAAAAAAAA=="}, {"n": "E:/napoly/server-data/resources//[standalone]/dpclothing/Client/Variations.lua", "mt": 1702509108, "s": 13136, "i": "euwBAAAABQAAAAAAAAAAAA=="}, {"n": "E:/napoly/server-data/resources//[standalone]/dpclothing/Locale/de.lua", "mt": 1702509108, "s": 1520, "i": "fOwBAAAABQAAAAAAAAAAAA=="}, {"n": "E:/napoly/server-data/resources//[standalone]/dpclothing/Locale/en.lua", "mt": 1702509108, "s": 1375, "i": "fewBAAAABQAAAAAAAAAAAA=="}, {"n": "E:/napoly/server-data/resources//[standalone]/dpclothing/Locale/es.lua", "mt": 1702509108, "s": 1540, "i": "fuwBAAAABQAAAAAAAAAAAA=="}, {"n": "E:/napoly/server-data/resources//[standalone]/dpclothing/Locale/fi.lua", "mt": 1702509108, "s": 1395, "i": "f+wBAAAABQAAAAAAAAAAAA=="}, {"n": "E:/napoly/server-data/resources//[standalone]/dpclothing/Locale/fr.lua", "mt": 1702509108, "s": 1622, "i": "gOwBAAAABQAAAAAAAAAAAA=="}, {"n": "E:/napoly/server-data/resources//[standalone]/dpclothing/Locale/it.lua", "mt": 1702509108, "s": 1408, "i": "gewBAAAABQAAAAAAAAAAAA=="}, {"n": "E:/napoly/server-data/resources//[standalone]/dpclothing/Locale/tr.lua", "mt": 1702509108, "s": 1905, "i": "guwBAAAABQAAAAAAAAAAAA=="}, {"n": "E:/napoly/server-data/resources//[standalone]/dpclothing/fxmanifest.lua", "mt": 1738927643, "s": 481, "i": "c+wBAAAABQAAAAAAAAAAAA=="}]