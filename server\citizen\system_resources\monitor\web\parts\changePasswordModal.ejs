<!-- Change password modal -->
<div class="modal fade" id="modChangePassword" tabindex="-1" role="dialog" aria-labelledby="modChangePassword-title" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modChangePassword-title">Change your password</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center" id="modChangePassword-body">
                <div class="form-group row">
                    <% if (isTempPassword) { %>
                        <script>const isTempPassword = true;</script>
                        <div class="col-sm-8 mx-auto">
                            <h5 class="text-danger mx-auto">Your account has a temporary password that needs to be changed.</h5>
                        </div>
                    <% } else { %>
                        <div class="col-sm-8 mx-auto">
                            <label for="modChangePassword-oldPassword" class="col-sm-12 col-form-label">
                                Enter your <b>current</b> password
                            </label>
                            <div class="input-group">
                                <input type="password" autocomplete="new-password" class="form-control text-center"
                                    id="modChangePassword-oldPassword" maxlength="64">
                            </div>
                        </div>
                        <div class="col-sm-12" style="margin-top: 1.75em;"></div>
                    <% } %>


                    <div class="col-sm-8 mx-auto">
                        <label for="modChangePassword-newPassword" class="col-sm-12 col-form-label">
                            Enter a <b>new</b> password
                        </label>
                        <div class="input-group">
                            <input type="password" autocomplete="new-password" class="form-control text-center"
                                id="modChangePassword-newPassword" maxlength="24">
                        </div>
                    </div>
                    <div class="col-sm-8 mx-auto">
                        <label for="modChangePassword-confirmPassword" class="col-sm-12 col-form-label">
                            Confirm your <b>new</b> password
                        </label>
                        <div class="input-group">
                            <input type="password" autocomplete="new-password" class="form-control text-center"
                                id="modChangePassword-confirmPassword">
                        </div>
                    </div>
                    <div class="col-sm-12">
                        <span class="form-text text-muted text-center">
                            The new password has to be between 6 and 24 characters.
                        </span>
                    </div>
                </div>
            </div>
            <div class="modal-footer text-center">
                <div class="mx-auto">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" id="modChangePassword-save">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>
