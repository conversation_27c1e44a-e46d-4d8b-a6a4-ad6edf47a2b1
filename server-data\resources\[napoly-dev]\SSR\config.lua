Config = {}

-- إعدادات عامة للنظام
Config.Debug = true -- تفعيل/تعطيل رسائل التتبع

-- إعدادات التنسيق مع cd_easytime
Config.CDEasyTime = {
    Enabled = true,                    -- تفعيل التنسيق مع cd_easytime
    PauseOnCountdown = true,          -- إيقاف مزامنة cd_easytime أثناء العد التنازلي
    RestoreAfterCountdown = true,     -- إعادة تفعيل المزامنة بعد انتهاء العد التنازلي
    SyncDelay = 1000,                 -- تأخير إعادة المزامنة (بالميلي ثانية)
}

-- إعدادات صوت التسونامي
Config.TsunamiSiren = {
    SoundFile = "tsunami_siren",  -- اسم ملف الصوت
    Volume = 0.8,                -- مستوى الصوت (0.0 - 1.0)
    Duration = 30000,            -- مدة تشغيل الصوت (30 ثانية)
    MaxDistance = 500.0          -- المسافة القصوى لسماع الصوت
}

-- إعدادات المراحل الزمنية (بالثواني)
Config.WeatherPhases = {
    CALM = 300,        -- 5 دقائق - طقس عادي
    WINDY = 240,       -- 4 دقائق - رياح قوية + غيوم قليلة
    HALLOWEEN = 180,   -- 3 دقائق - طقس الهالوين + رياح قوية + غيوم كثيفة
    PROGRESSIVE_RAIN = 120, -- 2 دقيقة - إضافة مطر تدريجي (بدون تغيير الطقس)
    THUNDER_STORM = 60 -- 1 دقيقة - العاصفة الرعدية الكاملة
}

-- إعدادات الطقس لكل مرحلة
Config.Weather = {
    TransitionTime = 15.0,     -- وقت الانتقال بين حالات الطقس (بالثواني)
    SyncEnabled = true,        -- تفعيل المزامنة مع cd_easytime
    DefaultWeather = 'CLEAR',  -- الطقس الافتراضي

    -- الدقيقة 3: طقس الهالوين
    Halloween = {
        WeatherType = 'HALLOWEEN',
        RainLevel = 0.0,
        CloudOpacity = 0.3,
        WindSpeed = 15.0,
        WindDirection = 'random'
    },

    -- الدقيقة 2: غيوم + مطر + رياح
    CloudyRain = {
        WeatherType = 'RAIN',
        RainLevel = 0.6,
        CloudOpacity = 0.8,
        WindSpeed = 10.0,
        WindDirection = 'random'
    },

    -- الدقيقة 1: العاصفة الرعدية الكاملة
    ThunderStorm = {
        WeatherType = 'THUNDER',
        RainLevel = 1.0,
        CloudOpacity = 1.0,
        WindSpeed = 20.0,
        WindDirection = 'random',
        ThunderEnabled = true,
        FlashIntensity = 1.0,
        ThunderInterval = {
            Min = 3000,  -- 3 ثواني
            Max = 8000   -- 8 ثواني
        }
    },
    
    -- إعدادات التأثيرات الخاصة
    SpecialEffects = {
        Dawn = {
            StartHour = 5,
            EndHour = 7,
            Intensity = 0.3
        },
        Dusk = {
            StartHour = 18,
            EndHour = 20,
            Intensity = 0.3
        }
    }
}

-- إعدادات فحص الخارج/الداخل
Config.LocationCheck = {
    StartOffset = 0.1,
    RayHeight = 20.0,
    IgnorePlayer = 1,
    IgnoreVehicles = 1,
    RayType = 1
}

-- إعدادات تأثيرات الضوء والرعد
Config.LightingEffects = {
    CycleName = "lightning",    -- اسم دورة الإضاءة
    NormalDuration = 1500,      -- مدة الوميض العادي
    IntenseDuration = 2000      -- مدة الوميض القوي
}

-- إعدادات الرعد للدقيقة الأخيرة
Config.Thunder = {
    SoundFile = "thunder",      -- اسم ملف صوت الرعد
    Volume = 0.7,               -- مستوى الصوت
    MaxDistance = 400.0,        -- المسافة القصوى لسماع الصوت
    FlashIntensity = 1.0,       -- شدة الوميض
    MinInterval = 3000,         -- الحد الأدنى بين الرعود (3 ثواني)
    MaxInterval = 8000          -- الحد الأقصى بين الرعود (8 ثواني)
}

-- إعدادات تأثير الزلزال مع الرعد
Config.Earthquake = {
    Enabled = true,                    -- تفعيل تأثير الزلزال
    ShakeIntensity = 0.8,             -- شدة اهتزاز الشاشة (0.0 - 2.0)
    ShakeDuration = 2000,             -- مدة الاهتزاز (بالميلي ثانية)
    StumbleChance = 35,               -- احتمال التعثر (%)
    StumbleForce = 15.0,              -- قوة دفع التعثر
    StumbleDuration = 3000,           -- مدة التعثر (بالميلي ثانية)
    OnlyOutdoors = true,              -- تأثير فقط في الخارج
    ShakeType = "SMALL_EXPLOSION_SHAKE", -- نوع الاهتزاز
    Debug = true                      -- رسائل التتبع للزلزال
}

-- إعدادات Discord Webhook
Config.DiscordWebhook = {
    url = "https://discord.com/api/webhooks/1372200058342346882/a8TWp4L_9odyOEisQon4QSqfCkIi775f1p9CUKJ4RMk_Asqll_mcmFpmRyTWDldX0vky",      -- رابط Webhook الخاص بك
    botName = "Server Restarter",  -- اسم البوت
    avatar = "https://c0.klipartz.com/pngpicture/739/431/gratis-png-tormenta-de-tornado-vendaval-ciclon-tropical-tornado-thumbnail.png",                   -- رابط الصورة الرمزية (اختياري)
    colors = {
        default = 16711680,        -- أحمر
        yellow = 15335168,         -- أصفر
        green = 65280,             -- أخضر
        red = 16711680             -- أحمر
    }
}  


Config.DefaultRestartTime = 300  -- 5 دقائق (بالثواني)

-- إعدادات الإشعارات
Config.Notifications = {
    -- تنسيق إشعارات إعادة التشغيل
    Restart = {
        template = '<div style="position: fixed; top: 20%; left: 50%; transform: translateX(-50%); background-color: rgb(255, 172, 51); color: white; padding: 12px 24px; border-radius: 4px; font-family: Arial, sans-serif; display: flex; align-items: center; gap: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"><i class="fas fa-exclamation-triangle" style="color: white;"></i><div><div style="font-weight: bold; margin-bottom: 4px;">Server Announcement by txAdmin:</div>{0}</div></div>',
        color = {255, 172, 51}
    },
    -- تنسيق إشعارات الإلغاء
    Cancel = {
        template = '<div style="position: fixed; top: 20%; left: 50%; transform: translateX(-50%); background-color: rgb(40, 167, 69); color: white; padding: 12px 24px; border-radius: 4px; font-family: Arial, sans-serif; display: flex; align-items: center; gap: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"><i class="fas fa-check-circle" style="color: white;"></i><div><div style="font-weight: bold; margin-bottom: 4px;">Server Announcement:</div>{0}</div></div>',
        color = {40, 167, 69}
    },
    -- رسائل الإشعارات
    Messages = {
        restart = "سيتم إعادة تشغيل السيرفر خلال %d دقائق.",
        cancel = "تم إلغاء عملية إعادة تشغيل السيرفر."
    }
}


-- Timer Settings | إعدادات المؤقت
Config.DefaultRestartTime = 300 -- وقت العد التنازلي الافتراضي بالثواني (5 دقائق)



-- UI Settings | إعدادات الواجهة
Config.UI = {
    position = {
        right = "20px", -- المسافة من اليمين
        top = "50%"     -- المسافة من الأعلى
    },
    warningTime = 30 -- الوقت بالثواني عندما يبدأ المؤقت بالوميض
}

Config.Commands = {
    startRestart = 'srestart',
    cancelRestart = 'crestart'
}

Config.AdminGroups = {
    ['superadmin'] = true,
    ['admin'] = true
}

return Config


