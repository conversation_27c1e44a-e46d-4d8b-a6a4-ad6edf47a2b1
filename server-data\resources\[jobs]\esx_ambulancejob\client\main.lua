local firstSpawn, PlayerLoaded = true, false
local id, name = GetPlayerServerId(PlayerId()), GetPlayerName(PlayerId())
isDead, isSearched, medic = false, false, 0
local is_want_set_in_srer = false
local streetName, playerGender
local blips = {}
--[[Citizen.CreateThread(function()
	while true do
		Citizen.Wait(1000)

		local playerCoords = GetEntityCoords(PlayerPedId())
		streetName,ts = GetStreetNameAtCoord(playerCoords.x, playerCoords.y, playerCoords.z)
		streetName = GetStreetNameFromHashKey(streetName)
		print(streetName)
	end
end)]]

RegisterNetEvent('esx_ambulancejob:setTrueClient')
AddEventHandler('esx_ambulancejob:setTrueClient', function()
	print('[+] successfully set true')
	is_want_set_in_srer = true
end)

RegisterNetEvent('esx_ambulancejob:setFalseClient')
AddEventHandler('esx_ambulancejob:setFalseClient', function()
	print('[+] successfully set false')
	is_want_set_in_srer = false
end)

TriggerEvent('skinchanger:getSkin', function(skin)
	playerGender = skin.sex
end)

AddEventHandler("onClientMapStart", function()
	exports.spawnmanager:spawnPlayer()
	Citizen.Wait(5000)
	exports.spawnmanager:setAutoSpawn(false)
end)

Citizen.CreateThread(function()

	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(100)
	end
	PlayerLoaded = true
	ESX.PlayerData = ESX.GetPlayerData()
	SetPedMaxTimeUnderwater(GetPlayerPed(-1), 50.00)
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
	ESX.PlayerData = xPlayer
	PlayerLoaded = true
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	ESX.PlayerData.job = job
	print("Job updated to:", job.name)
end)

--[[ Old
AddEventHandler('esx:onPlayerSpawn', function()
	isDead = false
	if firstSpawn then
		firstSpawn = false

		if Config.AntiCombatLog then
			while not PlayerLoaded do
				Citizen.Wait(5000)
			end

			ESX.TriggerServerCallback('esx_ambulancejob:getDeathStatus', function(shouldDie)
				if shouldDie then
					Citizen.Wait(10000)
					SetEntityHealth(PlayerPedId(), 0)
				end
			end)
		end
	end
end)
--]]

-- متغير لتتبع ما إذا كان اللاعب قد تم إحياؤه من المستشفى
local justRespawned = false

AddEventHandler('esx:onPlayerSpawn', function()
	-- إذا كان اللاعب قد تم إحياؤه للتو من المستشفى، لا نطبق AntiCombatLog
	if justRespawned then
		print("[DEBUG] justRespawned is true, skipping AntiCombatLog")
		isDead = false
		return
	end

	print("[DEBUG] justRespawned is false, checking firstSpawn: " .. tostring(firstSpawn))
	if firstSpawn then
		firstSpawn = false

		if Config.AntiCombatLog then
			while not PlayerLoaded do
				Citizen.Wait(5000)
			end

			ESX.TriggerServerCallback('esx_ambulancejob:getDeathStatus', function(shouldDie)
				if shouldDie then
					Citizen.Wait(1000)
					ESX.ShowNotification(_U('combatlog_message'))

					-- إعداد اللاعب لحالة النزيف
					Citizen.Wait(500)
					local playerPed = PlayerPedId()

					-- تطبيق حالة النزيف (صحة منخفضة جداً وليس موت مباشر)
					SetEntityHealth(playerPed, 1) -- صحة منخفضة جداً للنزيف
					isDead = true

					-- تحميل الأنيميشن مسبقاً
					RequestAnimDict('random@dealgonewrong')
					while not HasAnimDictLoaded('random@dealgonewrong') do
						Citizen.Wait(0)
					end

					-- تطبيق أنيميشن النزيف فوراً
					TaskPlayAnim(playerPed, "random@dealgonewrong", "idle_a", 1.0, 1.0, -1, 1, 0, 0, 0, 0)

					-- تطبيق تأثيرات النزيف
					ESX.UI.Menu.CloseAll()
					TriggerServerEvent('esx_ambulancejob:setDeathStatus', true)
					TriggerEvent('esx_misc:hidehud', true)

					-- بدء العد التنازلي للنزيف
					StartDeathTimer()
					StartDistressSignal()
					StartScreenEffect('DeathFailOut', 0, false)

					-- حماية إضافية للتأكد من بقاء اللاعب في حالة النزيف
					Citizen.CreateThread(function()
						while isDead do
							Citizen.Wait(1000)
							local currentHealth = GetEntityHealth(PlayerPedId())
							-- التأكد من أن الصحة تبقى منخفضة (حالة النزيف)
							if currentHealth > 1 then
								SetEntityHealth(PlayerPedId(), 1)
							elseif currentHealth <= 0 then
								SetEntityHealth(PlayerPedId(), 1) -- منع الموت المباشر
							end
							-- التأكد من استمرار الأنيميشن
							if not IsEntityPlayingAnim(PlayerPedId(), "random@dealgonewrong", "idle_a", 3) then
								TaskPlayAnim(PlayerPedId(), "random@dealgonewrong", "idle_a", 1.0, 1.0, -1, 1, 0, 0, 0, 0)
							end
						end
					end)
				else
					-- إذا لم يكن اللاعب ميتاً، نعين isDead = false
					isDead = false
				end
			end)
		else
			-- إذا لم يكن AntiCombatLog مفعلاً، نعين isDead = false
			isDead = false
		end
	else
		-- إذا لم يكن firstSpawn، نعين isDead = false
		isDead = false
	end
end)

--[[ Create blips ORIGINAL Config.Hospitals // NOT WORKING NOW OLD
Citizen.CreateThread(function()
	for k,v in pairs(Config.Hospitals) do
		local blip = AddBlipForCoord(v.Blip.coords)

		SetBlipSprite(blip, v.Blip.sprite)
		SetBlipScale(blip, v.Blip.scale)
		SetBlipColour(blip, v.Blip.color)
		SetBlipAsShortRange(blip, true)

		BeginTextCommandSetBlipName('STRING')
		AddTextComponentSubstringPlayerName(_U('hospital'))
		EndTextCommandSetBlipName(blip)
	end
end)]]

--[Create blips CUSTOM Config.HospitalsBlips // multi blips
Citizen.CreateThread(function()
	for k,v in pairs(Config.HospitalsBlips) do
		local blip = AddBlipForCoord(v.coords)

		SetBlipSprite(blip, v.sprite)
		SetBlipScale(blip, v.scale)
		SetBlipColour(blip, v.color)
		SetBlipAsShortRange(blip, true)

		BeginTextCommandSetBlipName('STRING')

		AddTextComponentSubstringPlayerName(v.info)

		EndTextCommandSetBlipName(blip)
	end
end)

local watertimer = 0

function MoveToRoad()
	local playerPed = PlayerPedId()
	local playercoords = GetEntityCoords(playerPed)
	watertimer = 500
	while true do
		Citizen.Wait(0)
		if watertimer > 0 and isDead then
			watertimer = watertimer -1
			SetTextFont(font)
			SetTextProportional(1)
			SetTextScale(0.0, 0.5)
			SetTextColour(255, 0, 0, 255)
			SetTextDropshadow(0, 0, 0, 0, 255)
			SetTextEdge(1, 0, 0, 0, 255)
			SetTextDropShadow()
			SetTextOutline()
			SetTextEntry("STRING")
			AddTextComponentString("<FONT FACE='A9eelsh'>ﺀﺎﻤﻟﺍ ﺝﺭﺎﺧ ﻚﻠﻘﻧ ﻢﺘﻳ ﻑﻮﺳ</font>")
			DrawText(0.400, 0.400)
		else
			break
		end
	end
	if isDead then
		local _, closstRd, anotPos = GetClosestRoad(playercoords.x, playercoords.y, playercoords.z, 10, 1, true)
		SetEntityCoords(playerPed, closstRd)
	end
end

-- Disable most inputs when dead
Citizen.CreateThread(function()
	while true do
		local Sleep = 1500

		if isDead then
			Sleep = 0
			DisableAllControlActions(0)
			EnableControlAction(0, 47, true)
			EnableControlAction(0, 245, true)
			EnableControlAction(0, 38, true)
			local playerPed = PlayerPedId()
			if IsEntityInWater(playerPed) and watertimer == 0 then
				MoveToRoad()
			end
		else
			Citizen.Wait(500)
		end
		Citizen.Wait(Sleep)
	end
end)

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
		if isDead and isSearched then
			local playerPed = PlayerPedId()
			local ped = GetPlayerPed(GetPlayerFromServerId(medic))
			isSearched = false

			AttachEntityToEntity(playerPed, ped, 11816, 0.54, 0.54, 0.0, 0.0, 0.0, 0.0, false, false, false, false, 2, true)
			Citizen.Wait(1000)
			DetachEntity(playerPed, true, false)
			ClearPedTasksImmediately(playerPed)
		end
	end
end)

RegisterNetEvent('esx_ambulancejob:clsearch')
AddEventHandler('esx_ambulancejob:clsearch', function(medicId)
	local playerPed = PlayerPedId()

	if isDead then
		local coords = GetEntityCoords(playerPed)
		local playersInArea = ESX.Game.GetPlayersInArea(coords, 50.0)

		for i=1, #playersInArea, 1 do
			local player = playersInArea[i]
			if player == GetPlayerFromServerId(medicId) then
				medic = tonumber(medicId)
				isSearched = true
				break
			end
		end
	end
end)

--[[
function OnPlayerDeath()
	isDead = true
	ESX.UI.Menu.CloseAll()
	TriggerServerEvent('esx_ambulancejob:setDeathStatus', true)
	TriggerEvent('esx_misc:hidehud', true)

	StartDeathTimer()
	StartDistressSignal()

	StartScreenEffect('DeathFailOut', 0, false)
end]]

function OnPlayerDeath()
	-- إذا كان اللاعب قد تم إحياؤه للتو، لا نطبق الموت
	if justRespawned then
		print("[DEBUG] OnPlayerDeath called but justRespawned is true, ignoring")
		return
	end

	if isDead ==  true then return end
	local second = 30
	   isDead = true

	   -- تشغيل صوت الموت المخصص
	   TriggerServerEvent('InteractSound_SV:PlayOnSource', 'death', 0.5)

	   -- إخفاء الهود عند الموت
	   TriggerEvent('codem-blackhudv2:SetForceHide', true)

	   Citizen.CreateThread(function()

	   repeat

	   Citizen.Wait(1000 * second)
	   ClearPedTasksImmediately(PlayerPedId())

	   until isDead == false

	   end)

	ESX.UI.Menu.CloseAll()
	TriggerServerEvent('esx_ambulancejob:setDeathStatus', true)

	TriggerEvent('esx_misc:hidehud', true)

	StartDeathTimer()
	StartDistressSignal()

	StartScreenEffect('DeathFailOut', 0, false)
	StartDeathAnim()
	Wait(2000)
	SetPedComponentVariation(PlayerPedId(), 9, 0, 0, 2)
end

Citizen.CreateThread(function()
	while isDead do
		Citizen.Wait(500)
	end
	local alpha = 255
	fontId = RegisterFontId('A9eelsh')
	local white = {r=255,g=255,b=255}
	local scale = 0.55

	while true do
		Citizen.Wait(1)

		if isDead then
			--id
			SetTextColour(white.r, white.g, white.b, alpha)
			SetTextFont(fontId)
			SetTextScale(scale, scale)
			SetTextWrap(0.0, 1.0)
			SetTextCentre(false)
			SetTextDropshadow(2, 2, 0, 0, 0)
			SetTextEdge(1, 0, 0, 0, 205)
			SetTextOutline()
			SetTextEntry("STRING")
			AddTextComponentString('<FONT FACE = "A9eelsh">~y~ '..id..' ~w~ ﺮﻓﺮﻴﺴﻟﺎﺑ ﻚﻤﻗﺭ')
			DrawText(0.435, 0.930)
	end
	end
end)

local inbed = false

RegisterNetEvent('wais:inBed', function(bed)
	print('Gelen bed:', bed)
	inbed = bed
end)

RegisterCommand('hamadatest', function()
	local ped = PlayerPedId()
	ClearPedTasks(ped)
end)

function StartDeathAnim()
	local ped = PlayerPedId()
	local pos = GetEntityCoords(ped)
    local heading = GetEntityHeading(ped)
	ClearPedTasks(ped)

	RequestAnimDict('random@dealgonewrong')
    while not HasAnimDictLoaded('random@dealgonewrong') do
        Citizen.Wait(0)
    end

	Citizen.Wait(1000)
	-- ResurrectPed(ped)
	local killerHash = GetPedCauseOfDeath(ped)
	NetworkResurrectLocalPlayer(pos.x, pos.y, pos.z, heading, false, false)
	SetEntityVisible(ped, true, 0)
	-- if IsMeleeWeapon(killerHash) then
	-- 	NetworkResurrectLocalPlayer(pos.x, pos.y, pos.z, heading, false, false)
	-- 	SetEntityVisible(ped, true, 0)
	-- end
	SetEntityVisible(ped, true, 0)
	SetEntityHealth(ped, 200)

	-- SetEntityInvincible(ped,true)

	ClearPedTasksImmediately(ped)

	SetEntityHealth(ped, GetEntityMaxHealth(ped))

	Citizen.CreateThread(function()
		while true do
			Citizen.Wait(1000)
			if isDead and not is_want_set_in_srer and not inbed then
				TaskPlayAnim(ped, "random@dealgonewrong", "idle_a", 1.0, 1.0, -1, 1, 0, 0, 0, 0)
				-- SetEntityInvincible(ped,true)
			elseif is_want_set_in_srer then
				--print()
			else
				break
			end
		end
	end)
end

function IsMeleeWeapon(weaponHash)
    for _, hash in ipairs(meleeWeaponHashes) do
        if weaponHash == hash then
            return true
        end
    end
    return false
end

-- List of melee weapon hashes
meleeWeaponHashes = {
    GetHashKey("WEAPON_KNUCKLE"),
    GetHashKey("WEAPON_KNIFE"),
    GetHashKey("WEAPON_NIGHTSTICK"),
    GetHashKey("WEAPON_HAMMER"),
    GetHashKey("WEAPON_BAT"),
    GetHashKey("WEAPON_GOLFCLUB"),
    GetHashKey("WEAPON_CROWBAR"),
    GetHashKey("WEAPON_BOTTLE"),
    GetHashKey("WEAPON_DAGGER"),
    GetHashKey("WEAPON_HATCHET"),
    GetHashKey("WEAPON_MACHETE"),
    GetHashKey("WEAPON_SWITCHBLADE"),
    GetHashKey("WEAPON_POOLCUE"),
    GetHashKey("WEAPON_WRENCH")
}


RegisterNetEvent('esx_ambulancejob:useItem')
AddEventHandler('esx_ambulancejob:useItem', function(itemName)
	ESX.UI.Menu.CloseAll()

	if itemName == 'medikit' then
		local lib, anim = 'anim@heists@narcotics@funding@gang_idle', 'gang_chatting_idle01' -- TODO better animations
		local playerPed = PlayerPedId()

		ESX.Streaming.RequestAnimDict(lib, function()
			TaskPlayAnim(playerPed, lib, anim, 8.0, -8.0, -1, 0, 0, false, false, false)

			Citizen.Wait(500)
			while IsEntityPlayingAnim(playerPed, lib, anim, 3) do
				Citizen.Wait(0)
				DisableAllControlActions(0)
			end

			TriggerEvent('esx_ambulancejob:heal', 'big', true)
			ESX.ShowNotification(_U('used_medikit'))
		end)

	elseif itemName == 'bandage' then
		local lib, anim = 'anim@heists@narcotics@funding@gang_idle', 'gang_chatting_idle01' -- TODO better animations
		local playerPed = PlayerPedId()

		ESX.Streaming.RequestAnimDict(lib, function()
			TaskPlayAnim(playerPed, lib, anim, 8.0, -8.0, -1, 0, 0, false, false, false)

			Citizen.Wait(500)
			while IsEntityPlayingAnim(playerPed, lib, anim, 3) do
				Citizen.Wait(0)
				DisableAllControlActions(0)
			end

			TriggerEvent('esx_ambulancejob:heal', 'small', true)
			ESX.ShowNotification(_U('used_bandage'))
		end)
	end
end)

function StartDistressSignal()
	Citizen.CreateThread(function()
		local timer = Config.BleedoutTimer

		while timer > 0 and isDead do
			Citizen.Wait(0)
			timer = timer - 30

			fontId = RegisterFontId('A9eelsh')
			SetTextFont(fontId)
			SetTextScale(0.50, 0.50)
			SetTextColour(185, 185, 185, 255)
			SetTextDropshadow(0, 0, 0, 0, 255)
			SetTextDropShadow()
			SetTextOutline()
			BeginTextCommandDisplayText('STRING')
			AddTextComponentSubstringPlayerName(_U('distress_send'))
			EndTextCommandDisplayText(0.390, 0.730)

			if IsControlJustReleased(0, 47) then
				SendDistressSignal()
				break
			end
		end
	end)
end

press = false
RegisterNetEvent('quasarphone:adddeathblip')
AddEventHandler('quasarphone:adddeathblip', function(coords)
	local blipcoords = vector3(coords.x, coords.y, coords.z)
  	local blip = AddBlipForCoord(blipcoords)
  	SetBlipSprite(blip, 280)
    SetBlipColour(blip, 17)
    SetBlipCategory(blip, 7)
    BeginTextCommandSetBlipName('STRING')
    AddTextComponentSubstringPlayerName("Help!")
    EndTextCommandSetBlipName(blip)
    ESX.ShowNotification('<center>لتحديد الموقع <FONT COLOR=red>G</FONT> اظغط </center>', 8000)
    press = true
    Citizen.CreateThread(function()
        while press do
        Citizen.Wait(0)
          if IsControlJustReleased(0, 47) then
            SetNewWaypoint(coords.x, coords.y)
            press = false
          end
        end
    end)
    Citizen.Wait(7000)
    press = false
    local blipShown = true
    Citizen.CreateThread(function()
		while blipShown do
		  Citizen.Wait(1)
			if not IsBlipFlashing(blip) then
			  SetBlipScale(blip, 1.1 + math.sin(GetGameTimer() / 200) * 0.4)
		 	end
		end
	  end)
    Wait(5 * 60 * 1000)
    blipShown = false
    RemoveBlip(blip)
end)

function SendDistressSignal()
	local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local myPos = GetEntityCoords(PlayerPedId())
    local GPS = 'GPS: ' .. myPos.x .. ', ' .. myPos.y .. ', ' .. myPos.z
	TriggerServerEvent('esx_ambulancejob:playerdeadblip', coords)
	TriggerServerEvent('esx_phone:send', 'ambulance', _U('distress_message'), false, coords)
	local message = "شخص مسقط يحتاج مساعدة" -- The message that will be received.
	local alert = {
		message = message,
		-- img = "img url", -- You can add image here (OPTIONAL).
		location = coords,
	}
	if Config.QuasarPhone then
		TriggerServerEvent('qs-smartphone:server:sendJobAlert', alert, "ambulance") -- "Your ambulance job"
		TriggerServerEvent('qs-smartphone:server:AddNotifies', {
			head = "الهلال الأحمر", -- Message name.
			msg = message,
			app = 'business'
		})
		TriggerServerEvent('quasarphone:adddeathblip', coords)
	end
	TriggerEvent('esx_adminjob:checkMessageIsGet', 'ambulance', 'يوجد شخص مسقط بحاجة الى انعاش | ' .. GPS)
	-- TriggerServerEvent('gcphone:SendMessageToLeoJob', 'ambulance', 'يوجد شخص مسقط بحاجة الى انعاش | ' .. GPS)
	--TriggerServerEvent("esx:emitMessagetoems",GetPlayerServerId(playerPed))
	--TriggerEvent("pNotify:SetQueueMax", "distress_sent", 1)
	TriggerEvent("pNotify:SendNotification",{
 				                text = "<b style='color:#FFAE00'><font size=5><center>".._U('distress_sent').."</center></b>",
					            type = "alert",
					            timeout = (10000),
					            layout = "centerLeft",
					            killer = true,
					            queue = "distress_sent",
								sounds = {
							    sources = {"Radio.wav"}, -- For sounds to work, you place your sound in the html folder and then add it to the files array in the __resource.lua file.
							    volume = 0.1,
							    conditions = {"docVisible"}
					            }
				            })
   	Citizen.Wait(11000)
	TriggerEvent("pNotify:SendNotification",{
 				                text = "<b style='color:#F6F6F6'><font size=5><center>سوف يظهر لك اختيار الانتقال للمستشفى مقابل رسوم بعد انقضاء الوقت المحدد</center></b>",
					            type = "info",
					            timeout = (15000),
					            layout = "centerLeft",
					            killer = false,
					            queue = "distress_sent"
				            })

	Citizen.Wait(16000)
	TriggerEvent("pNotify:SendNotification",{
 				                text = "<b style='color:#F6F6F6'><font size=5><center>اختيارك الانتقال للمستشفى اثناء حضور الشرطة والاسعاف او  قبل انتهاء الرول بلاي الذي اشتركت به او بدأته بنفسك يعتبر <font color=#FFAE00>مخالف للرول بلاي</br><font color=#FF0E0E>العقوبة طرد او بان</center></b>",
					            type = "info",
					            timeout = (30000),
					            layout = "centerLeft",
					            killer = false,
					            queue = "distress_sent"
				            })

	TriggerServerEvent('esx_ambulancejob:onPlayerDistress')
end

RegisterNetEvent('esx_ambulancejob:playerdeadblip:client', function(src, coords)
	if ESX.PlayerData.job and ESX.PlayerData.job.name == 'ambulance' then
			-- local player = GetPlayerFromServerId(playerId)
			-- 	local playerPed = GetPlayerPed(player)
			print(src, 'src')
				blips[src] = AddBlipForCoord(coords)

				SetBlipSprite(blips[src], 280)
				SetBlipColour(blips[src], 1)
				SetBlipFlashes(blips[src], true)
				SetBlipCategory(blips[src], 7)

				BeginTextCommandSetBlipName('STRING')
				AddTextComponentSubstringPlayerName(_U('blip_dead'))
				EndTextCommandSetBlipName(blips[src])
				Wait(3 * 60 * 1000)
				RemoveBlip(blips[src])
	end
end)

function DrawGenericTextThisFrame()
fontId = RegisterFontId('A9eelsh')
	SetTextFont(fontId)
	SetTextScale(0.0, 0.5)
	SetTextColour(255, 255, 255, 255)
	SetTextDropshadow(0, 0, 0, 0, 255)
	SetTextDropShadow()
	SetTextOutline()
	SetTextCentre(true)
end

function secondsToClock(seconds)
	local seconds, hours, mins, secs = tonumber(seconds), 0, 0, 0

	if seconds <= 0 then
		return 0, 0
	else
		local hours = string.format('%02.f', math.floor(seconds / 3600))
		local mins = string.format('%02.f', math.floor(seconds / 60 - (hours * 60)))
		local secs = string.format('%02.f', math.floor(seconds - hours * 3600 - mins * 60))

		return mins, secs
	end
end

function StartDeathTimer()
	local canPayFine = false

	if Config.EarlyRespawnFine then
		ESX.TriggerServerCallback('esx_ambulancejob:checkBalance', function(canPay)
			canPayFine = canPay
		end)
	end

	local earlySpawnTimer = ESX.Math.Round(Config.EarlyRespawnTimer / 1000)
	local bleedoutTimer = ESX.Math.Round(Config.BleedoutTimer / 1000)

	Citizen.CreateThread(function()
		-- early respawn timer
		while earlySpawnTimer > 0 and isDead do
			Citizen.Wait(1000)

			if earlySpawnTimer > 0 then
				earlySpawnTimer = earlySpawnTimer - 1
			end
		end

		-- bleedout timer
		while bleedoutTimer > 0 and isDead do
			Citizen.Wait(1000)

			if bleedoutTimer > 0 then
				bleedoutTimer = bleedoutTimer - 1
			end
		end
	end)

	Citizen.CreateThread(function()
		local text, timeHeld
		StartScreenEffect('DeathFailOut', 0, false)
		-- early respawn timer
		while earlySpawnTimer > 0 and isDead do
			Citizen.Wait(0)
			text = _U('respawn_available_in', secondsToClock(earlySpawnTimer))

			DrawGenericTextThisFrame()

			SetTextEntry('STRING')
			AddTextComponentString(text)
			EndTextCommandDisplayText(0.505, 0.640)
		end

		-- bleedout timer
		while bleedoutTimer > 0 and isDead do
			Citizen.Wait(0)
			text = _U('respawn_bleedout_in', secondsToClock(bleedoutTimer))

			if not Config.EarlyRespawnFine then
				text = text .. _U('respawn_bleedout_prompt')

				if IsControlPressed(0, 38) and timeHeld > 60 then
					RemoveItemsAfterRPDeath(1)
					break
				end
			elseif Config.EarlyRespawnFine and canPayFine then
				text2 = _U('respawn_bleedout_fine', ESX.Math.GroupDigits(Config.EarlyRespawnFineAmount))

				if IsControlPressed(0, 38) and timeHeld > 60 then
					TriggerServerEvent('esx_ambulancejob:payFine')
					RemoveItemsAfterRPDeath(1)
					break
				end
			end

			if IsControlPressed(0, 38) then
				timeHeld = timeHeld + 1
			else
				timeHeld = 0
			end

			DrawGenericTextThisFrame()

			SetTextEntry("STRING")
			AddTextComponentString(text2)
			DrawText(0.505, 0.830)

			DrawGenericTextThisFrame()

			SetTextEntry("STRING")
			AddTextComponentString(text)
			DrawText(0.505, 0.640)
		end

		if bleedoutTimer < 1 and isDead then
			RemoveItemsAfterRPDeath(0)
		end
	end)
end

function RemoveItemsAfterRPDeath(deathType) -- 0 = player bleedout | 1 = player select go to hospital
	TriggerServerEvent('esx_ambulancejob:setDeathStatus', false)

	Citizen.CreateThread(function()
		DoScreenFadeOut(800)

		while not IsScreenFadedOut() do
			Citizen.Wait(10)
		end

		local ped = PlayerPedId()
		local pedCoords = GetEntityCoords(ped)
		local nearHospitalDistance = 100000000
		local nearHospitalcoords = {}
		local nearHospitalheading = 0
		local hospitalLabel = ''

		for k,v in pairs(Config.RespawnPoint) do
			if exports.esx_jail.isPlayerJailed() then
				nearHospitalcoords = Config.jailedRespawnPoint.coords
				nearHospitalheading = Config.jailedRespawnPoint.heading
				hospitalLabel = Config.jailedRespawnPoint.label
				break
			else
				checkDistance = GetDistanceBetweenCoords(pedCoords, v.coords, true)

				if checkDistance < nearHospitalDistance then
					nearHospitalDistance = checkDistance
					nearHospitalcoords = v.coords
					nearHospitalheading = v.heading
					hospitalLabel = v.label
				end
			end
		end

		local coordss = 'aj%3y@d'
		TriggerServerEvent('esx_ambulancejob:chat', deathType, hospitalLabel, coordss)
		TriggerEvent('esx_misc:hidehud', false)

		-- إحياء اللاعب فعلياً قبل النقل
		local ped = PlayerPedId()
		NetworkResurrectLocalPlayer(pedCoords.x, pedCoords.y, pedCoords.z, GetEntityHeading(ped), true, false)
		SetPlayerInvincible(ped, false)
		ClearPedBloodDamage(ped)

		-- إعادة تعيين الصحة والحالات
		SetEntityHealth(ped, GetEntityMaxHealth(ped))
		TriggerEvent('esx_status:set', 'hunger', 1000000)
		TriggerEvent('esx_status:set', 'thirst', 1000000)

		-- إيقاف تأثير الموت
		StopScreenEffect('DeathFailOut')

		isDead = false

		-- إظهار الهود عند الذهاب للمستشفى
		TriggerEvent('codem-blackhudv2:SetForceHide', false)
		ESX.TriggerServerCallback('esx_ambulancejob:removeItemsAfterRPDeath', function()

			ESX.SetPlayerData('lastPosition', nearHospitalcoords)
			ESX.SetPlayerData('loadout', {})

			TriggerServerEvent('esx:updateLastPosition', nearHospitalcoords)
			RespawnPed(PlayerPedId(), nearHospitalcoords, nearHospitalheading)

			DoScreenFadeIn(800)
		end)
	end)
end

function RespawnPed(ped, coords, heading)
	-- تعيين المتغير لمنع تطبيق AntiCombatLog عند الإحياء (قبل الإحياء)
	print("[DEBUG] RespawnPed called, setting justRespawned = true")
	justRespawned = true

	SetEntityCoordsNoOffset(ped, coords.x, coords.y, coords.z, false, false, false, true)
	NetworkResurrectLocalPlayer(coords.x, coords.y, coords.z, heading, true, false)
	SetPlayerInvincible(ped, false)
	ClearPedBloodDamage(ped)
	ESX.UI.Menu.CloseAll()

	print("[DEBUG] About to trigger esx:onPlayerSpawn")
	TriggerServerEvent('esx:onPlayerSpawn')
	TriggerEvent('esx:onPlayerSpawn')
	TriggerEvent('playerSpawned') -- compatibility with old scripts, will be removed soon

	TriggerServerEvent("esx_ambulancejob:DeadStatus", false)

	-- إظهار الهود عند الولادة (تأكيد إضافي)
	TriggerEvent('codem-blackhudv2:SetForceHide', false)

	-- إعادة تعيين المتغير بعد تأخير قصير لضمان اكتمال جميع الأحداث
	Citizen.CreateThread(function()
		Citizen.Wait(3000) -- انتظار 3 ثوان
		print("[DEBUG] Resetting justRespawned to false after delay")
		justRespawned = false
	end)
end

RegisterNetEvent('esx_phone:loaded')
AddEventHandler('esx_phone:loaded', function(phoneNumber, contacts)
	local specialContact = {
		name       = 'Ambulance',
		number     = 'ambulance',
		base64Icon = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEwAACxMBAJqcGAAABp5JREFUWIW1l21sFNcVhp/58npn195de23Ha4Mh2EASSvk0CPVHmmCEI0RCTQMBKVVooxYoalBVCVokICWFVFVEFeKoUdNECkZQIlAoFGMhIkrBQGxHwhAcChjbeLcsYHvNfsx+zNz+MBDWNrYhzSvdP+e+c973XM2cc0dihFi9Yo6vSzN/63dqcwPZcnEwS9PDmYoE4IxZIj+ciBb2mteLwlZdfji+dXtNU2AkeaXhCGteLZ/X/IS64/RoR5mh9tFVAaMiAldKQUGiRzFp1wXJPj/YkxblbfFLT/tjq9/f1XD0sQyse2li7pdP5tYeLXXMMGUojAiWKeOodE1gqpmNfN2PFeoF00T2uLGKfZzTwhzqbaEmeYWAQ0K1oKIlfPb7t+7M37aruXvEBlYvnV7xz2ec/2jNs9kKooKNjlksiXhJfLqf1PXOIU9M8fmw/XgRu523eTNyhhu6xLjbSeOFC6EX3t3V9PmwBla9Vv7K7u85d3bpqlwVcvHn7B8iVX+IFQoNKdwfstuFtWoFvwp9zj5XL7nRlPXyudjS9z+u35tmuH/lu6dl7+vSVXmDUcpbX+skP65BxOOPJA4gjDicOM2PciejeTwcsYek1hyl6me5nhNnmwPXBhjYuGC699OpzoaAO0PbYJSy5vgt4idOPrJwf6QuX2FO0oOtqIgj9pDU5dCWrMlyvXf86xsGgHyPeLos83Brns1WFXLxxgVBorHpW4vfQ6KhkbUtCot6srns1TLPjNVr7+1J0PepVc92H/Eagkb7IsTWd4ZMaN+yCXv5zLRY9GQ9xuYtQz4nfreWGdH9dNlkfnGq5/kdO88ekwGan1B3mDJsdMxCqv5w2Iq0khLs48vSllrsG/Y5pfojNugzScnQXKBVA8hrX51ddHq0o6wwIlgS8Y7obZdUZVjOYLC6e3glWkBBVHC2RJ+w/qezCuT/2sV6Q5VYpowjvnf/iBJJqvpYBgBS+w6wVB5DLEOiTZHWy36nNheg0jUBs3PoJnMfyuOdAECqrZ3K7KcACGQp89RAtlysCphqZhPtRzYlcPx+ExklJUiq0le5omCfOGFAYn3qFKS/fZAWS7a3Y2wa+GJOEy4US+B3aaPUYJamj4oI5LA/jWQBt5HIK5+JfXzZsJVpXi/ac8+mxWIXWzAG4Wb4g/jscNMp63I4U5FcKaVvsNyFALokSA47Kx8PVk83OabCHZsiqwAKEpjmfUJIkoh/R+L9oTpjluhRkGSPG4A7EkS+Y3HZk0OXYpIVNy01P5yItnptDsvtIwr0SunqoVP1GG1taTHn1CloXm9aLBEIEDl/IS2W6rg+qIFEYR7+OJTesqJqYa95/VKBNOHLjDBZ8sDS2998a0Bs/F//gvu5Z9NivadOc/U3676pEsizBIN1jCYlhClL+ELJDrkobNUBfBZqQfMN305HAgnIeYi4OnYMh7q/AsAXSdXK+eH41sykxd+TV/AsXvR/MeARAttD9pSqF9nDNfSEoDQsb5O31zQFprcaV244JPY7bqG6Xd9K3C3ALgbfk3NzqNE6CdplZrVFL27eWR+UASb6479ULfhD5AzOlSuGFTE6OohebElbcb8fhxA4xEPUgdTK19hiNKCZgknB+Ep44E44d82cxqPPOKctCGXzTmsBXbV1j1S5XQhyHq6NvnABPylu46A7QmVLpP7w9pNz4IEb0YyOrnmjb8bjB129fDBRkDVj2ojFbYBnCHHb7HL+OC7KQXeEsmAiNrnTqLy3d3+s/bvlVmxpgffM1fyM5cfsPZLuK+YHnvHELl8eUlwV4BXim0r6QV+4gD9Nlnjbfg1vJGktbI5UbN/TcGmAAYDG84Gry/MLLl/zKouO2Xukq/YkCyuWYV5owTIGjhVFCPL6J7kLOTcH89ereF1r4qOsm3gjSevl85El1Z98cfhB3qBN9+dLp1fUTco+0OrVMnNjFuv0chYbBYT2HcBoa+8TALyWQOt/ImPHoFS9SI3WyRajgdt2mbJgIlbREplfveuLf/XXemjXX7v46ZxzPlfd8YlZ01My5MUEVdIY5rueYopw4fQHkbv7/rZkTw6JwjyalBCHur9iD9cI2mU0UzD3P9H6yZ1G5dt7Gwe96w07dl5fXj7vYqH2XsNovdTI6KMrlsAXhRyz7/C7FBO/DubdVq4nBLPaohcnBeMr3/2k4fhQ+Uc8995YPq2wMzNjww2X+vwNt1p00ynrd2yKDJAVN628sBX1hZIdxXdStU9G5W2bd9YHR5L3f/CNmJeY9G8WAAAAAElFTkSuQmCC'
	}

	TriggerEvent('esx_phone:addSpecialContact', specialContact.name, specialContact.number, specialContact.base64Icon)
end)

AddEventHandler('esx:onPlayerDeath', function(data)
	-- إذا كان اللاعب قد تم إحياؤه للتو، لا نطبق الموت
	if justRespawned then
		print("[DEBUG] esx:onPlayerDeath called but justRespawned is true, ignoring")
		return
	end

	print("[DEBUG] esx:onPlayerDeath called, calling OnPlayerDeath()")
	OnPlayerDeath()
	TriggerServerEvent("esx_ambulancejob:DeadStatus", true)

	-- إخفاء الهود عند الموت (تأكيد إضافي)
	TriggerEvent('codem-blackhudv2:SetForceHide', true)
end)

RegisterNetEvent('esx_ambulancejob:revive')
AddEventHandler('esx_ambulancejob:revive', function()
	local playerPed = PlayerPedId()
	local coords = GetEntityCoords(playerPed)
	TriggerServerEvent('esx_ambulancejob:setDeathStatus', false)
	DoScreenFadeOut(800)

	while not IsScreenFadedOut() do
		Citizen.Wait(50)
	end

	local formattedCoords = {
		x = ESX.Math.Round(coords.x, 1),
		y = ESX.Math.Round(coords.y, 1),
		z = ESX.Math.Round(coords.z, 1)
	}

	RespawnPed(playerPed, formattedCoords, 0.0)
	StopScreenEffect('DeathFailOut')
	DoScreenFadeIn(800)
	TriggerEvent('esx_status:set', 'hunger', 1000000)
    TriggerEvent('esx_status:set', 'thirst', 1000000)

	isDead = false
	TriggerEvent('esx_misc:hidehud', false) -- show hud agen after revive

	-- إظهار الهود عند الإحياء
	TriggerEvent('codem-blackhudv2:SetForceHide', false)
end)

RegisterNetEvent('esx_ambulancejob:revive_alldeads')
AddEventHandler('esx_ambulancejob:revive_alldeads', function()
	if isDead then
	local playerPed = PlayerPedId()
	local coords = GetEntityCoords(playerPed)
	TriggerServerEvent('esx_ambulancejob:setDeathStatus', false)
	DoScreenFadeOut(800)

	while not IsScreenFadedOut() do
		Citizen.Wait(50)
	end

	local formattedCoords = {
		x = ESX.Math.Round(coords.x, 1),
		y = ESX.Math.Round(coords.y, 1),
		z = ESX.Math.Round(coords.z, 1)
	}

	RespawnPed(playerPed, formattedCoords, 0.0)
	StopScreenEffect('DeathFailOut')
	DoScreenFadeIn(800)

	isDead = false
	TriggerEvent('esx_misc:hidehud', false) -- show hud agen after revive

	-- إظهار الهود عند الإحياء
	TriggerEvent('codem-blackhudv2:SetForceHide', false)
end
end)

-- Load unloaded IPLs
if Config.LoadIpl then
	RequestIpl('Coroner_Int_on') -- Morgue
end
