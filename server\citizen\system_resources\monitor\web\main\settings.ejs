<%- await include('parts/header.ejs', locals) %>

<!-- Page CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.61.1/codemirror.min.css" 
    integrity="sha512-xIf9AdJauwKIVtrVRZ0i4nHP61Ogx9fSRAkCLecmE2dL/U8ioWpDvFCAy4dcfecN72HHB9+7FfQj3aiO68aaaw==" 
    crossorigin="anonymous" referrerpolicy="no-referrer" />
<link rel="stylesheet" href="<%= resourcePath %>css/codemirror_lucario.css?txVer=<%= txAdminVersion %>">
<style>
    .cm-s-lucario{
        font-size: 1.05rem !important;
    }
    .CodeMirror{
        min-height: min(calc(100vh - 250px), 450px);
    }

    /* https://codemirror.net/5/addon/dialog/dialog.css */
    .CodeMirror-dialog {
        position: absolute;
        left: 0; right: 0;
        background: inherit;
        z-index: 15;
        padding: .1em .8em;
        overflow: hidden;
        color: inherit;
    }
    .CodeMirror-dialog-top {
        border-bottom: 1px solid #eee;
        top: 0;
    }
    .CodeMirror-dialog-bottom {
        border-top: 1px solid #eee;
        bottom: 0;
    }
    .CodeMirror-dialog input {
        border: none;
        outline: none;
        background: transparent;
        width: 20em;
        color: inherit;
        font-family: monospace;
    }
    .CodeMirror-dialog button {
        font-size: 70%;
    }
</style>


<div class="row justify-content-md-center">
    <div class="col-md-7 mw-col8">

        <!-- Settings Card -->
        <div class="card card-accent-danger">
            <div class="card-header font-weight-bold" style="border-bottom: 0px;">
                <ul class="nav nav-tabs card-header-tabs" id="tabSelector">
                    <li class="nav-item">
                        <a class="nav-item nav-link <%= activeTab === 'global' ? 'active' : '' %>" id="nav-global-tab" data-toggle="tab" href="#nav-global"
                            role="tab" aria-controls="nav-global" aria-selected="true">Global</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-item nav-link <%= activeTab === 'fxserver' ? 'active' : '' %>" id="nav-fxserver-tab" data-toggle="tab" href="#nav-fxserver"
                            role="tab" aria-controls="nav-fxserver" aria-selected="true">FXServer</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-item nav-link <%= activeTab === 'monitor' ? 'active' : '' %>" id="nav-monitor-tab" data-toggle="tab" href="#nav-monitor"
                            role="tab" aria-controls="nav-monitor" aria-selected="true">Restarter</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-item nav-link <%= activeTab === 'playerManager' ? 'active' : '' %>" id="nav-playerManager-tab" data-toggle="tab" href="#nav-playerManager"
                            role="tab" aria-controls="nav-playerManager" aria-selected="true">
                            Player Manager
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-item nav-link <%= activeTab === 'discord' ? 'active' : '' %>" id="nav-discord-tab" data-toggle="tab" href="#nav-discord"
                            role="tab" aria-controls="nav-discord" aria-selected="true">
                            Discord
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-item nav-link <%= activeTab === 'menu' ? 'active' : '' %>" id="nav-menu-tab" data-toggle="tab" href="#nav-menu" role="tab" aria-controls="nav-menu" aria-selected="true">
                            Game
                            <!-- DynamicNewBadge reference, but static -->
                            <span class="badge badge-danger" style="padding-left: 0.25rem; padding-right: 0.25rem;">NEW</span>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="card-body settings-card">
                <div class="tab-content" id="nav-tabContent">

                    <!-- MARK: Global Tab -->
                    <div class="tab-pane fade <%= activeTab === 'global' ? 'active show' : '' %>" id="nav-global" role="tabpanel"
                        aria-labelledby="nav-global-tab">
                        <div class="form-group row">
                            <label for="frmGlobal-serverName" class="col-sm-3 col-form-label">
                                Server Name
                                <span class="text-danger">*</span>
                            </label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="frmGlobal-serverName"
                                    value="<%= (global.serverName !== 'undefined') ? global.serverName : '' %>" maxlength="24" placeholder="server name" <%= readOnly ? 'disabled' : '' %>>
                                <span class="form-text text-muted">
                                   A <strong>short</strong> server name to be used in txAdmin interface and Chat/Discord messages.
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="frmGlobal-language" class="col-sm-3 col-form-label">
                                Language
                            </label>
                            <div class="col-sm-9">
                                <select class="form-control" id="frmGlobal-language" <%= readOnly ? 'disabled' : '' %>>
                                    <% for (const locale of locales) { %>
                                        <option value="<%= locale.code %>" <%= global.language === locale.code ? 'selected' : '' %>><%= locale.label %></option>
                                    <% } %>
                                </select>
                                <span class="form-text text-muted">
                                    The language to use on Chat/Discord messages. <br>
                                    <b>Note:</b> For the Custom option, read the <a href="https://github.com/tabarra/txAdmin/blob/master/docs/translation.md" target="_blank" rel="noopener noreferrer">documentation</a>.
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">
                                Ban Templates
                            </label>
                            <div class="col-sm-9">
                                <button
                                    class="btn btn-sm btn-secondary"
                                    type="submit"
                                    onclick="navigateParentTo('/settings/ban-templates')"
                                >
                                    <i class="icon-pencil"></i> Edit Ban Templates
                                </button>
                                <span class="form-text text-muted">
                                    Configure ban reasons and durations that will appear as dropdown options when banning a player. This is useful for common reasons that happen frequently, like violation of your server rules.
                                </span>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <button class="btn btn-sm btn-primary" type="submit" id="frmGlobal-save" <%= readOnly ? 'disabled' : '' %>>
                                <i class="icon-check"></i> Save Global Settings
                            </button>
                        </div>
                    </div>

                    <!-- MARK: FXServer Tab -->
                    <div class="tab-pane fade <%= activeTab === 'fxServer' ? 'active show' : '' %>" id="nav-fxserver" role="tabpanel" aria-labelledby="nav-fxserver-tab">
                        <div class="form-group row">
                            <label for="frmFXServer-serverDataPath" class="col-sm-3 col-form-label">Server Data Folder
                                <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="frmFXServer-serverDataPath" value="<%= fxserver.serverDataPath || '' %>"
                                    placeholder="C:/Users/<USER>/Desktop/server01" <%= readOnly ? 'disabled' : '' %>>
                                <span class="form-text text-muted">
                                    The folder that <strong>contains</strong> the <code>resources</code> and
                                    <code>cache</code> folders, usually it's where you put your <code>server.cfg</code>. <br>
                                    <% if (isZapHosting) { %>
                                        <span class="font-weight-bold">ZAP-Hosting warning: this path must start with <code><%= txDataPath %></code>.</span>
                                    <% } %>
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="frmFXServer-cfgPath" class="col-sm-3 col-form-label">CFG File Path
                                <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="frmFXServer-cfgPath" value="<%= fxserver.cfgPath || '' %>"
                                    placeholder="C:/Users/<USER>/Desktop/server01/server.cfg" <%= readOnly ? 'disabled' : '' %>>
                                <span class="form-text text-muted">
                                    The path to your server config file, usually named <code>server.cfg</code>. <br>
                                    This can either be absolute, or relative to the Server Data folder.
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="frmFXServer-commandLine" class="col-sm-3 col-form-label">
                                Additional Arguments
                            </label>
                            <div class="col-sm-9">
                                <textarea id="frmFXServer-commandLine" rows="1" class="form-control expandable-textarea" placeholder="+set sv_enforceGameBuild 2545" <%= readOnly ? 'disabled' : '' %>><%= fxserver.commandLine || '' %></textarea>
                                <span class="form-text text-muted">
                                    Additional command-line arguments to pass to the FXServer instance. <br>
                                    For example, you can add <code>+set</code> and <code>+exec</code> commands. <br>
                                    <b>Note:</b> These commands can be set inside the <code>server.cfg</code> as well.
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="frmFXServer-onesync" class="col-sm-3 col-form-label">OneSync</label>
                            <div class="col-sm-9">
                                <select class="form-control" id="frmFXServer-onesync" <%= readOnly ? 'disabled' : '' %>>
                                    <option value="off" <%= fxserver.onesync === 'off' ? 'selected' : '' %>>Off</option>
                                    <option value="legacy" <%= fxserver.onesync === 'legacy' ? 'selected' : '' %>>Legacy (default)</option>
                                    <option value="on" <%= fxserver.onesync === 'on' ? 'selected' : '' %>>On (with infinity)</option>
                                </select>
                                <span class="form-text text-muted">
                                    Required if your server has more than 48 slots. <br>
                                    <b>Note:</b> Setting to <code>on</code> will override <code>onesync_enableInfinity</code> if set as an Additional Argument above. <br>
                                    <b>Note:</b> Do not attempt to set onesync in the <code>server.cfg</code> file.
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Autostart</label>
                            <div class="col-sm-9">
                                <label class="c-switch c-switch-label c-switch-pill c-switch-success fix-pill-form">
                                    <input class="c-switch-input" type="checkbox" id="frmFXServer-autostart"
                                        <%= fxserver.autostart || '' %> <%= readOnly ? 'disabled' : '' %>>
                                    <span class="c-switch-slider" data-checked="On" data-unchecked="Off"></span>
                                </label>
                                <span class="form-text text-muted">
                                    Start the server automatically after <strong>txAdmin</strong> starts.
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Quiet Mode</label>
                            <div class="col-sm-9">
                                <label class="c-switch c-switch-label c-switch-pill c-switch-success fix-pill-form">
                                    <input class="c-switch-input" type="checkbox" id="frmFXServer-quiet"
                                        <%= fxserver.quiet || '' %> <%= readOnly ? 'disabled' : '' %>>
                                    <span class="c-switch-slider" data-checked="Yes" data-unchecked="No"></span>
                                </label>
                                <span class="form-text text-muted">
                                    Do not print FXServer's output to the terminal. <br>
                                    You will still be able to use the Live Console.
                                </span>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <button class="btn btn-sm btn-primary" type="submit" id="frmFXServer-save" <%= readOnly ? 'disabled' : '' %>>
                                <i class="icon-check"></i> Save FXServer Settings
                            </button>
                        </div>
                    </div>

                    <!-- MARK: Restarter Tab -->
                    <div class="tab-pane fade <%= activeTab === 'monitor' ? 'active show' : '' %>" id="nav-monitor" role="tabpanel" aria-labelledby="nav-monitor-tab">
                        <div class="form-group row">
                            <label for="frmMonitor-resourceStartingTolerance" class="col-sm-3 col-form-label">
                                Resource Starting <br>
                                Time Limit
                            </label>
                            <div class="col-sm-9">
                                <select class="form-control" id="frmMonitor-resourceStartingTolerance" <%= readOnly ? 'disabled' : '' %>>
                                    <option value="90" <%= monitor.resourceStartingTolerance === 90 ? 'selected' : '' %>>1.5 minutes (default)</option>
                                    <option value="180" <%= monitor.resourceStartingTolerance === 180 ? 'selected' : '' %>>3 minutes</option>
                                    <option value="300" <%= monitor.resourceStartingTolerance === 300 ? 'selected' : '' %>>5 minutes</option>
                                    <option value="600" <%= monitor.resourceStartingTolerance === 600 ? 'selected' : '' %>>10 minutes</option>
                                </select>
                                <span class="form-text text-muted">
                                    At server boot, how much time to wait for any single resource to start before restarting the server. <br>
                                    <strong>Note:</strong> If you are getting <code>failed to start in time</code> errors, increase this value.
                                </span>
                            </div>
                        </div>
                        
                        <div class="form-group row">
                            <label for="frmMonitor-restarterSchedule" class="col-sm-3 col-form-label">Scheduled Restarts</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="frmMonitor-restarterSchedule" value="<%= monitor.restarterSchedule || '' %>"
                                    placeholder="05:00, 13:00" <%= readOnly ? 'disabled' : '' %>>
                                <span class="form-text text-danger" id="frmMonitor-timezoneAlert"></span>
                                <span class="form-text text-muted">
                                    At which times of day to restart the server. Leave it blank to disable this feature. <br>
                                    The time MUST be in the 24-hour format with two digits for hours as well as
                                    minutes (<code>HH:MM</code>) and the times should be separated by commas. <br>
                                    <strong>Note:</strong> Make sure your schedule matches your server time and not your local time.
                                </span>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <button class="btn btn-sm btn-primary" type="submit" id="frmMonitor-save" <%= readOnly ? 'disabled' : '' %>>
                                <i class="icon-check"></i> Save Monitor/Restarter Settings
                            </button>
                        </div>
                    </div>
                    
                    <!-- MARK: Player Manager Tab -->
                    <div class="tab-pane fade <%= activeTab === 'playerManager' ? 'active show' : '' %>" id="nav-playerManager" role="tabpanel" aria-labelledby="nav-playerManager-tab">
                        <div class="form-group row">
                            <label for="frmPlayerManager-whitelistMode" class="col-sm-3 col-form-label">
                                Whitelist Mode
                            </label>
                            <div class="col-sm-9">
                                <select class="form-control" id="frmPlayerManager-whitelistMode" <%= readOnly ? 'disabled' : '' %>>
                                    <option value="disabled" <%= playerDatabase.whitelistMode === 'disabled' ? 'selected' : '' %>>Disabled</option>
                                    <option value="adminOnly" <%= playerDatabase.whitelistMode === 'adminOnly' ? 'selected' : '' %>>Admin-only (maintenance mode)</option>
                                    <option value="guildMember" <%= playerDatabase.whitelistMode === 'guildMember' ? 'selected' : '' %>>Discord Guild Member</option>
                                    <option value="guildRoles" <%= playerDatabase.whitelistMode === 'guildRoles' ? 'selected' : '' %>>Discord Guild Roles</option>
                                    <option value="approvedLicense" <%= playerDatabase.whitelistMode === 'approvedLicense' ? 'selected' : '' %>>Approved License</option>
                                </select>
                                <span class="form-text text-muted">
                                    <p class="form-text text-muted mb-2">
                                        <strong class="text-secondary">Disabled:</strong> No whitelist status will be checked by txAdmin.
                                    </p>
                                    <p class="form-text text-muted mb-2">
                                        <strong class="text-secondary">Admin-only:</strong> Will only allow server join if your <code>fivem:</code> or <code>discord:</code> identifiers are attached to a txAdmin administrator. Also known as maintenance mode.
                                    </p>
                                    <p class="form-text text-muted mb-2">
                                        <strong class="text-secondary">Discord Guild Member:</strong> Checks if the player joining has a <code>discord:</code> identifier and is present in the Discord guild configured in the Discord Tab.
                                    </p>
                                    <p class="form-text text-muted mb-2">
                                        <strong class="text-secondary">Discord Guild Roles:</strong> Checks if the player joining has a <code>discord:</code> identifier and is present in the Discord guild configured in the Discord Tab and has at least one of the roles specified below.
                                    </p>
                                    <p class="form-text text-muted mb-2">
                                        <strong class="text-secondary">Approved License:</strong> The player <code>license:</code> identifier must be whitelisted by a txAdmin administrator. This can be done through the <a href="#" onclick="navigateParentTo('/whitelist')">Whitelist page</a>, or the <code>/whitelist</code> Discord bot slash command.
                                    </p>
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="frmPlayerManager-whitelistRejectionMessage" class="col-sm-3 col-form-label">
                                Whitelist Custom <br>
                                Rejection Message
                            </label>
                            <div class="col-sm-9">
                                <textarea id="frmPlayerManager-whitelistRejectionMessage" rows="3" class="form-control expandable-textarea" placeholder="Please join http://discord.gg/example and request to be whitelisted." <%= readOnly ? 'disabled' : '' %>><%= playerDatabase.whitelistRejectionMessage || '' %></textarea>
                                <span class="form-text text-muted">
                                    Optional message to display to a player on the rejection message that shows when they try to connect while not being whitelisted. <br>
                                    If you have a Discord whitelisting process, you can put here links to your guild.
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="frmPlayerManager-whitelistedDiscordRoles" class="col-sm-3 col-form-label">
                                Whitelisted Roles
                            </label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="frmPlayerManager-whitelistedDiscordRoles" value="<%= playerDatabase.whitelistedDiscordRoles || '' %>"
                                    placeholder="000000000000000000, 000000000000000000" <%= readOnly ? 'disabled' : '' %>>
                                <span class="form-text text-muted">
                                    The ID of the Discord roles that are whitelisted to join the server. <br>
                                    This field supports multiple roles, separated by comma. <br>
                                    <strong>Note:</strong> Requires the whitelist mode to be set to "Discord Guild Roles".
                                </span>
                            </div>
                        </div>

                        <hr>

                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">
                                Enable Ban Checking
                            </label>
                            <div class="col-sm-9">
                                <label class="c-switch c-switch-label c-switch-pill c-switch-success fix-pill-form">
                                    <input class="c-switch-input" type="checkbox" id="frmPlayerManager-onJoinCheckBan"
                                        <%= playerDatabase.onJoinCheckBan || '' %> <%= readOnly ? 'disabled' : '' %>>
                                    <span class="c-switch-slider" data-checked="Yes" data-unchecked="No"></span>
                                </label>
                                <span class="form-text text-muted">
                                    Enable checking for ban status on player join. <br>
                                    <strong>Note:</strong> txAdmin bans will not work if this option is disabled.
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="frmPlayerManager-requiredBanHwidMatches" class="col-sm-3 col-form-label">
                                Required Ban <br>
                                HWID Matches
                            </label>
                            <div class="col-sm-9">
                                <select class="form-control" id="frmPlayerManager-requiredBanHwidMatches" <%= readOnly ? 'disabled' : '' %>>
                                    <option value="1" <%= playerDatabase.requiredBanHwidMatches === 1 ? 'selected' : '' %>>1 - recommended</option>
                                    <option value="2" <%= playerDatabase.requiredBanHwidMatches === 2 ? 'selected' : '' %>>2 - lax</option>
                                    <option value="3" <%= playerDatabase.requiredBanHwidMatches === 3 ? 'selected' : '' %>>3 - very lax</option>
                                    <option value="4" <%= playerDatabase.requiredBanHwidMatches === 4 ? 'selected' : '' %>>4 - virtually disabled</option>
                                    <option value="0" <%= playerDatabase.requiredBanHwidMatches === 0 ? 'selected' : '' %>>Disable HWID Bans</option>
                                </select>
                                <span class="form-text text-muted">
                                    This option configures how many HWID tokens must match between a player and an existing ban for the player join to be blocked, or can disable HWID Bans entirely. <br>
                                    Since Hardware ID Tokens are not guaranteed to be unique, there is the possibility of tokens from two players matching without them being related to each other. <br>
                                    <strong>Note:</strong> Most players have 3 to 6 HWID tokens.
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="frmPlayerManager-banRejectionMessage" class="col-sm-3 col-form-label">
                                Ban Custom <br>
                                Rejection Message
                            </label>
                            <div class="col-sm-9">
                                <textarea id="frmPlayerManager-banRejectionMessage" rows="3" class="form-control expandable-textarea" placeholder="You can join http://discord.gg/example to appeal this ban." <%= readOnly ? 'disabled' : '' %>><%= playerDatabase.banRejectionMessage || '' %></textarea>
                                <span class="form-text text-muted">
                                    Optional message to display to a player on the rejection message that shows when they try to connect while being banned. <br>
                                    If you have a ban appeal process, you can use this field to inform the players.
                                </span>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <button class="btn btn-sm btn-primary" type="submit" id="frmPlayerManager-save" <%= readOnly ? 'disabled' : '' %>>
                                <i class="icon-check"></i> Save Player Manager Settings
                            </button>
                        </div>
                    </div>

                    <!-- MARK: Discord Tab -->
                    <div class="tab-pane fade <%= activeTab === 'discord' ? 'active show' : '' %>" id="nav-discord" role="tabpanel" aria-labelledby="nav-discord-tab">
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Enable Bot</label>
                            <div class="col-sm-9">
                                <label class="c-switch c-switch-label c-switch-pill c-switch-success fix-pill-form">
                                    <input class="c-switch-input" type="checkbox" id="frmDiscord-enabled"
                                        <%= discord.enabled || '' %> <%= readOnly ? 'disabled' : '' %>>
                                    <span class="c-switch-slider" data-checked="Yes" data-unchecked="No"></span>
                                </label>
                                <span class="form-text text-muted">
                                    Enable Discord Integration.
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="frmDiscord-token" class="col-sm-3 col-form-label">
                                Token
                                <span class="text-danger">*</span>
                            </label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control blur-input" id="frmDiscord-token" 
                                    value="<%= readOnly ? '[redacted]' : (discord.token || '') %>"
                                    placeholder="xxxxxxxxxxxxxxxxxxxxxxxx.xxxxxx.xxxxxxxxxxxxxxxxxxxxxxxxxxx"
                                    maxlength="96" autocomplete="off" <%= readOnly ? 'disabled' : '' %>>
                                <span class="form-text text-muted">
                                    To get a token and the bot to join your server, follow these two guides:
                                    <a href="https://discordjs.guide/preparations/setting-up-a-bot-application.html"
                                        target="_blank" rel="noopener noreferrer">Setting up a bot application</a> and
                                    <a href="https://discordjs.guide/preparations/adding-your-bot-to-servers.html"
                                        target="_blank" rel="noopener noreferrer">Adding your bot to servers</a> <br>
                                    <strong>Note:</strong> Do not reuse the same token for another bot.  <br>
                                    <strong>Note:</strong> The bot requires the GUILD MEMBERS intent, which can be set at the
                                    <a href="https://discord.com/developers/applications" target="_blank" rel="noopener noreferrer">Discord Developer Portal</a>.
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="frmDiscord-guild" class="col-sm-3 col-form-label">
                                Guild/Server ID
                                <span class="text-danger">*</span>
                            </label>
                            <div class="col-sm-9">
                                <input type="number" class="form-control" id="frmDiscord-guild"
                                    value="<%= discord.guild || '' %>" 
                                    placeholder="000000000000000000" <%= readOnly ? 'disabled' : '' %>>
                                <span class="form-text text-muted">
                                    The ID of the Discord Guild (also known as Discord Server). <br>
                                    To get the Guild ID, go to Discord's settings and
                                    <a href="https://support.discordapp.com/hc/article_attachments/115002742731/mceclip0.png"
                                    target="_blank" > enable developer mode</a>, then right-click on the guild icon select "Copy ID".
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="frmDiscord-announceChannel" class="col-sm-3 col-form-label">
                                Announcements <br>
                                To Channel ID <br>
                                <i class="text-info">(optional)</i>
                            </label>
                            <div class="col-sm-9">
                                <input type="number" class="form-control" id="frmDiscord-announceChannel"
                                    value="<%= discord.announceChannel || '' %>" 
                                    placeholder="000000000000000000" <%= readOnly ? 'disabled' : '' %>>
                                <span class="form-text text-muted">
                                    The ID of the channel to send Announcements (eg server restarts). <br>
                                    You can leave it blank to disable this feature. <br>
                                    To get the channel ID, go to Discord's settings and
                                    <a href="https://support.discordapp.com/hc/article_attachments/115002742731/mceclip0.png"
                                    target="_blank" > enable developer mode</a>, then right-click on the channel name and select "Copy ID".
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">
                                Status Embed
                                <span class="text-danger">*</span>
                            </label>
                            <div class="col-sm-9">
                                <button
                                    class="btn btn-sm btn-secondary"
                                    type="submit"
                                    id="frmDiscord-editStatusEmbedJsonBtn"
                                    <%= readOnly ? 'disabled' : '' %>
                                >
                                    <i class="icon-pencil"></i> Change Embed JSON
                                </button>
                                <button
                                    class="btn btn-sm btn-secondary"
                                    type="submit"
                                    id="frmDiscord-editStatusEmbedConfigJsonBtn"
                                    <%= readOnly ? 'disabled' : '' %>
                                >
                                    <i class="icon-pencil"></i> Change Config JSON
                                </button>
                                <textarea
                                    id="frmDiscord-statusEmbedJson"
                                    class="d-none"
                                    autocomplete="off"
                                ><%= discord.embedJson || '' %></textarea>
                                <textarea
                                    id="frmDiscord-statusEmbedConfigJson"
                                    class="d-none"
                                    autocomplete="off"
                                ><%= discord.embedConfigJson || '' %></textarea>
                                <textarea
                                    id="frmDiscord-statusEmbedJsonDefault"
                                    class="d-none"
                                    autocomplete="off"
                                ><%= defaults.discord.embedJson || '' %></textarea>
                                <textarea
                                    id="frmDiscord-statusEmbedConfigJsonDefault"
                                    class="d-none"
                                    autocomplete="off"
                                ><%= defaults.discord.embedConfigJson || '' %></textarea>
                                <span class="form-text text-muted">
                                    The server status embed is customizable by editing the two JSONs above. <br>
                                    <strong>Note:</strong> Use the command <code>/status add</code> on a channel that the bot has the "Send Message" permission to setup the embed.
                                </span>
                            </div>
                        </div>


                        <div class="text-center mt-4">
                            <button class="btn btn-sm btn-primary" type="submit" id="frmDiscord-save" <%= readOnly ? 'disabled' : '' %>>
                                <i class="icon-check"></i> Save Discord Settings
                            </button>
                        </div>
                    </div>

                    <!-- MARK: Game/Menu Tab -->
                    <div class="tab-pane fade <%= activeTab === 'menu' ? 'active show' : '' %>" id="nav-menu" role="tabpanel" aria-labelledby="nav-menu-tab">
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Enable Menu</label>
                            <div class="col-sm-9">
                                <label class="c-switch c-switch-label c-switch-pill c-switch-success fix-pill-form">
                                    <input class="c-switch-input" type="checkbox" id="frmMenu-enabled"
                                        <%= global.menuEnabled || '' %> <%= readOnly ? 'disabled' : '' %>>
                                    <span class="c-switch-slider" data-checked="Yes" data-unchecked="No"></span>
                                </label>
                                <span class="form-text text-muted">
                                    When enabled, admins will be able to open the menu by typing <code>/tx</code> or using the keybind configured in the FiveM/RedM settings.
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Align Menu Right</label>
                            <div class="col-sm-9">
                                <label class="c-switch c-switch-label c-switch-pill c-switch-success fix-pill-form">
                                    <input class="c-switch-input" type="checkbox" id="frmMenu-alignRight"
                                        <%= global.menuAlignRight || '' %> <%= readOnly ? 'disabled' : '' %>>
                                    <span class="c-switch-slider" data-checked="Yes" data-unchecked="No"></span>
                                </label>
                                <span class="form-text text-muted">
                                    Move menu to the right side of the screen.
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="frmMenu-pageKey" class="col-sm-3 col-form-label">Menu Page Switch Key</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="frmMenu-pageKey"
                                    value="<%= global.menuPageKey || '' %>"
                                    placeholder="578045190955335691" <%= readOnly ? 'disabled' : '' %>>
                                <span class="form-text text-muted">
                                    The key used to to switch tabs in the menu. <br>
                                    Click above and press any key to change the configuration. <br>
                                    <strong>Note:</strong> The default is <code>Tab</code>, and you cannot use <code>Escape</code> or <code>Backspace</code>.
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">
                                Hide Admin Name <br>
                                In Punishments <br>
                                <!-- DynamicNewBadge reference, but static -->
                                <span class="badge badge-danger" style="padding-left: 0.25rem; padding-right: 0.25rem;">NEW</span>
                            </label>
                            <div class="col-sm-9">
                                <label class="switch-lg c-switch c-switch-label c-switch-pill c-switch-success fix-pill-form">
                                    <input class="c-switch-input" type="checkbox" id="frmMenu-hideAdminInPunishments"
                                        <%= global.hideAdminInPunishments || '' %> <%= readOnly ? 'disabled' : '' %>>
                                    <span class="c-switch-slider" data-checked="Hide" data-unchecked="Show"></span>
                                </label>
                                <span class="form-text text-muted">
                                    Never show to the players the admin name on <strong>Bans</strong> or <strong>Warns</strong>. <br>
                                    This information will still be available in the history and logs.
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">
                                Hide Admin Name <br>
                                In Messages <br>
                                <!-- DynamicNewBadge reference, but static -->
                                <span class="badge badge-danger" style="padding-left: 0.25rem; padding-right: 0.25rem;">NEW</span>
                            </label>
                            <div class="col-sm-9">
                                <label class="switch-lg c-switch c-switch-label c-switch-pill c-switch-success fix-pill-form">
                                    <input class="c-switch-input" type="checkbox" id="frmMenu-hideAdminInMessages"
                                        <%= global.hideAdminInMessages || '' %> <%= readOnly ? 'disabled' : '' %>>
                                    <span class="c-switch-slider" data-checked="Hide" data-unchecked="Show"></span>
                                </label>
                                <span class="form-text text-muted">
                                    Do not show the admin name on <strong>Announcements</strong> or <strong>DMs</strong>. <br>
                                    This information will still be available in the live console and logs.
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">
                                Hide Announcement <br>
                                Notification
                            </label>
                            <div class="col-sm-9">
                                <label class="switch-lg c-switch c-switch-label c-switch-pill c-switch-success fix-pill-form">
                                    <input class="c-switch-input" type="checkbox" id="frmMenu-hideDefaultAnnouncement"
                                        <%= global.hideDefaultAnnouncement || '' %> <%= readOnly ? 'disabled' : '' %>>
                                    <span class="c-switch-slider" data-checked="Hide" data-unchecked="Show"></span>
                                </label>
                                <span class="form-text text-muted">
                                    Suppresses the display of announcements, allowing you to implement your own announcement via the event <code>txAdmin:events:announcement</code>
                                    <a href="https://github.com/tabarra/txAdmin/blob/master/docs/menu.md" target="_blank" rel="noopener noreferrer">(docs page)</a>.
                                </span>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">
                                Hide Direct Message <br>
                                Notification
                            </label>
                            <div class="col-sm-9">
                                <label class="switch-lg c-switch c-switch-label c-switch-pill c-switch-success fix-pill-form">
                                    <input class="c-switch-input" type="checkbox" id="frmMenu-hideDefaultDirectMessage"
                                        <%= global.hideDefaultDirectMessage || '' %> <%= readOnly ? 'disabled' : '' %>>
                                    <span class="c-switch-slider" data-checked="Hide" data-unchecked="Show"></span>
                                </label>
                                <span class="form-text text-muted">
                                    Suppresses the display of direct messages, allowing you to implement your own direct message notification via the event <code>txAdmin:events:playerDirectMessage</code>
                                    <a href="https://github.com/tabarra/txAdmin/blob/master/docs/menu.md" target="_blank" rel="noopener noreferrer">(docs page)</a>.
                                </span>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">
                                Hide Warning <br>
                                Notification
                            </label>
                            <div class="col-sm-9">
                                <label class="switch-lg c-switch c-switch-label c-switch-pill c-switch-success fix-pill-form">
                                    <input class="c-switch-input" type="checkbox" id="frmMenu-hideDefaultWarning"
                                        <%= global.hideDefaultWarning || '' %> <%= readOnly ? 'disabled' : '' %>>
                                    <span class="c-switch-slider" data-checked="Hide" data-unchecked="Show"></span>
                                </label>
                                <span class="form-text text-muted">
                                    Suppresses the display of warnings, allowing you to implement your own warning via the event <code>txAdmin:events:playerWarned</code>
                                    <a href="https://github.com/tabarra/txAdmin/blob/master/docs/menu.md" target="_blank" rel="noopener noreferrer">(docs page)</a>.
                                </span>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">
                                Hide Scheduled Restart <br>
                                Warnings
                            </label>
                            <div class="col-sm-9">
                                <label class="switch-lg c-switch c-switch-label c-switch-pill c-switch-success fix-pill-form">
                                    <input class="c-switch-input" type="checkbox" id="frmMenu-hideDefaultScheduledRestartWarning"
                                        <%= global.hideDefaultScheduledRestartWarning || '' %> <%= readOnly ? 'disabled' : '' %>>
                                    <span class="c-switch-slider" data-checked="Hide" data-unchecked="Show"></span>
                                </label>
                                <span class="form-text text-muted">
                                    Suppresses the display of scheduled restart warnings, allowing you to implement your own warning via the event <code>txAdmin:events:scheduledRestart</code>
                                    <a href="https://github.com/tabarra/txAdmin/blob/master/docs/menu.md" target="_blank" rel="noopener noreferrer">(docs page)</a>.
                                </span>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <button class="btn btn-sm btn-primary" type="submit" id="frmMenu-save" <%= readOnly ? 'disabled' : '' %>>
                                <i class="icon-check"></i> Save Game Settings
                            </button>
                        </div>
                    </div>

                </div>
            </div>


        </div>

    </div>
</div>


<%- await include('parts/footer.ejs', locals) %>

<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.61.1/codemirror.min.js" 
    integrity="sha512-ZTpbCvmiv7Zt4rK0ltotRJVRaSBKFQHQTrwfs6DoYlBYzO1MA6Oz2WguC+LkV8pGiHraYLEpo7Paa+hoVbCfKw==" 
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="<%= resourcePath %>js/codeEditor/mode/yaml.js?txVer=<%= txAdminVersion %>"></script>
<script src="<%= resourcePath %>js/codeEditor/autorefresh.js?txVer=<%= txAdminVersion %>"></script>
<script src="<%= resourcePath %>js/codeEditor/searchcursor.js?txVer=<%= txAdminVersion %>"></script>
<script src="<%= resourcePath %>js/codeEditor/dialog.js?txVer=<%= txAdminVersion %>"></script>
<script src="<%= resourcePath %>js/codeEditor/search.js?txVer=<%= txAdminVersion %>"></script>

<!-- Embed JSON editors -->
<div class="modal fade" id="modJsonEditor" tabindex="-1" role="dialog" aria-labelledby="modJsonEditor-title" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document" style="max-width: min(730px, calc(100% - 30px));">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modJsonEditor-title">JSON Editor</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div class="modal-body">
                <div id="modJsonEditor-infoBody">
                    <p class="text-center">
                        For information regarding the placeholders and how to customize the embed, refer to <a href="https://github.com/tabarra/txAdmin/blob/master/docs/status_embed.md" target="_blank" rel="noopener noreferrer">our docs</a>. <br>
                        If you are having issue with the JSON formatting, you can use <a href="https://jsoneditoronline.org" id="modJsonEditor-externalEditorBtn" target="_blank" rel="noopener noreferrer">https://jsoneditoronline.org/</a>.
                    </p>
                    <textarea id="modJsonEditor-cmTarget" class="cms-s-lucario" name="code"></textarea>
                </div>
            </div>
            
            <div class="modal-footer text-center">
                <div class="mx-auto">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    &nbsp;
                    &nbsp;
                    <button type="button" class="btn btn-secondary" id="modJsonEditor-revertBtn">Revert to Default</button>
                    &nbsp;
                    &nbsp;
                    <button type="button" class="btn btn-success" id="modJsonEditor-saveBtn">Done</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- MARK: Save-->
<script>
    //MARK: Save General Settings
    const serverTimezone = "<%= serverTimezone %>";
    const browserTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    if(serverTimezone !== browserTimezone){
        const msg = `Server's timezone: <b>${serverTimezone}</b> <br>
            Your timezone: <b>${browserTimezone}</b> <br>
            Make sure to adjust the times to match your expectations.`;
        $('#frmMonitor-timezoneAlert').html(msg);
    }


    const resizeTextArea = (el) => {
        el.style.height = "auto";
        el.style.height = `${el.scrollHeight+2}px`;
    }

    const expandables = document.getElementsByClassName('expandable-textarea');
    for (const el of expandables) {
        el.onkeyup = (ev) =>{
            resizeTextArea(el);
        }
        el.onfocus = (ev) =>{
            resizeTextArea(el);
        }
    }

    const tabChangers = document.querySelectorAll('#tabSelector > li > a');
    for (const el of tabChangers) {
        el.onclick = (ev) => {
            setTimeout(() => {
                const visibleExpandables = document.querySelectorAll('#nav-tabContent .show .expandable-textarea');
                for (const el of visibleExpandables) {
                    resizeTextArea(el);
                }
            }, 500);
        }
    }

    //MARK: Save Global Settings
    $('#frmGlobal-save').click(function () {
        let data = {
            serverName: $('#frmGlobal-serverName').val().trim(),
            language: $('#frmGlobal-language').val(),
        }
        if (!data.serverName.length) {
            return $.notify({ message: 'The fields with "*" are required.' }, { type: 'warning' });
        }
        const notify = $.notify({ message: '<p class="text-center">Saving...</p>' }, {});

        txAdminAPI({
            type: "POST",
            url: '/settings/save/global',
            data: data,
            success: function (data) {
                if (checkApiLogoutRefresh(data)) return;
                updateMarkdownNotification(data, notify);
            },
            error: function (xmlhttprequest, textstatus, message) {
                notify.update('progress', 0);
                notify.update('type', 'danger');
                notify.update('message', message);
            }
        });
    });


    //MARK: Save FXServer Settings
    $('#frmFXServer-save').click(function () {
        let data = {
            serverDataPath: $('#frmFXServer-serverDataPath').val().trim(),
            cfgPath: $('#frmFXServer-cfgPath').val().trim(),
            commandLine: $('#frmFXServer-commandLine').val().trim(),
            onesync: $('#frmFXServer-onesync').val(),
            autostart: $('#frmFXServer-autostart').is(':checked'),
            quiet: $('#frmFXServer-quiet').is(':checked')
        }
        if (!data.serverDataPath.length || !data.cfgPath.length) {
            return $.notify({ message: 'The fields with "*" are required.' }, { type: 'warning' });
        }
        if (data.commandLine.includes(' onesync ')){
            return $.notify({ message: 'You cannot set onesync in Additional Arguments. Please use the selectbox below it.' }, { type: 'warning' });
        }
        //Removing menu beta convar (v4.9)
        data.commandLine = data.commandLine.replace(/\+?setr? txEnableMenuBeta true\s?/gi, '');
        const notify = $.notify({ message: '<p class="text-center">Saving...</p>' }, {});

        txAdminAPI({
            type: "POST",
            url: '/settings/save/fxserver',
            data: data,
            success: function (data) {
                if (checkApiLogoutRefresh(data)) return;
                updateMarkdownNotification(data, notify);
            },
            error: function (xmlhttprequest, textstatus, message) {
                notify.update('progress', 0);
                notify.update('type', 'danger');
                notify.update('message', message);
            }
        });
    });


    //MARK: Save Player Manager Settings
    $('#frmPlayerManager-save').click(function () {
        const data = {
            whitelistMode: $('#frmPlayerManager-whitelistMode').val(),
            whitelistRejectionMessage: $('#frmPlayerManager-whitelistRejectionMessage').val().trim(),
            whitelistedDiscordRoles: $('#frmPlayerManager-whitelistedDiscordRoles').val().trim(),
            onJoinCheckBan: $('#frmPlayerManager-onJoinCheckBan').is(':checked'),
            requiredBanHwidMatches: $('#frmPlayerManager-requiredBanHwidMatches').val(),
            banRejectionMessage: $('#frmPlayerManager-banRejectionMessage').val().trim(),
        }
        const notify = $.notify({ message: '<p class="text-center">Saving...</p>' }, {});

        txAdminAPI({
            type: "POST",
            url: '/settings/save/playerDatabase',
            data: data,
            success: function (data) {
                if (checkApiLogoutRefresh(data)) return;
                updateMarkdownNotification(data, notify);
            },
            error: function (xmlhttprequest, textstatus, message) {
                notify.update('progress', 0);
                notify.update('type', 'danger');
                notify.update('message', message);
            }
        });
    });


    //MARK: Save Monitor Settings
    $('#frmMonitor-save').click(function () {
        let data = {
            restarterSchedule: $('#frmMonitor-restarterSchedule').val().trim(),
            resourceStartingTolerance: $('#frmMonitor-resourceStartingTolerance').val(),
        }
        const notify = $.notify({ message: '<p class="text-center">Saving...</p>' }, {});

        txAdminAPI({
            type: "POST",
            url: '/settings/save/monitor',
            data: data,
            success: function (data) {
                if (checkApiLogoutRefresh(data)) return;
                updateMarkdownNotification(data, notify);
            },
            error: function (xmlhttprequest, textstatus, message) {
                notify.update('progress', 0);
                notify.update('type', 'danger');
                notify.update('message', message);
            }
        });
    });


    //MARK: Save Discord Settings
    let jsonBeingEdited = false;
    let defaultOfJsonBeingEdited = false;
    const discEls = {
        modal: document.getElementById('modJsonEditor'),
        title: document.getElementById('modJsonEditor-title'),
        cmTarget: document.getElementById('modJsonEditor-cmTarget'),
        externalEditorBtn: document.getElementById('modJsonEditor-externalEditorBtn'),
        saveBtn: document.getElementById('modJsonEditor-saveBtn'),
        revertBtn: document.getElementById('modJsonEditor-revertBtn'),
        embedJsonBtn: document.getElementById('frmDiscord-editStatusEmbedJsonBtn'),
        embedJsonText: document.getElementById('frmDiscord-statusEmbedJson'),
        embedJsonDefault: document.getElementById('frmDiscord-statusEmbedJsonDefault'),
        embedConfigJsonBtn: document.getElementById('frmDiscord-editStatusEmbedConfigJsonBtn'),
        embedConfigJsonText: document.getElementById('frmDiscord-statusEmbedConfigJson'),
        embedConfigJsonDefault: document.getElementById('frmDiscord-statusEmbedConfigJsonDefault'),
    }
    const setJsonEditor = (jsonString) => {
        if(window.CMInstance){
            window.CMInstance.toTextArea();
            window.CMInstance = null;
        }
        discEls.cmTarget.value = jsonString;
        window.CMInstance = CodeMirror.fromTextArea(discEls.cmTarget, {
            mode: "yaml", //couldn't find the json one
            autoRefresh:true,
            lineNumbers:true,
            lineWrapping: true,
            theme: "lucario"
        });
    }
    
    discEls.revertBtn.addEventListener('click', (e) => {
        setJsonEditor(defaultOfJsonBeingEdited);
    });
    discEls.embedJsonBtn.addEventListener('click', (e) => {
        discEls.title.textContent = 'Embed JSON Editor';
        jsonBeingEdited = discEls.embedJsonText;
        defaultOfJsonBeingEdited = discEls.embedJsonDefault.value;
        setJsonEditor(discEls.embedJsonText.value);
        $('#modJsonEditor').modal('show');
    });
    discEls.embedConfigJsonBtn.addEventListener('click', (e) => {
        discEls.title.textContent = 'Embed Config JSON Editor';
        jsonBeingEdited = discEls.embedConfigJsonText;
        defaultOfJsonBeingEdited = discEls.embedConfigJsonDefault.value;
        setJsonEditor(discEls.embedConfigJsonText.value);
        $('#modJsonEditor').modal('show');
    });
    discEls.saveBtn.addEventListener('click', (e) => {
        if(jsonBeingEdited){
            const editorData = window.CMInstance.getValue();
            //NOTE: don't need this anymore since we are using jsonrepair on the backend
            //It would be better to use it on the frontend, but no react, no luck.
            // try {
            //     const parsed = JSON.parse(editorData);
            // } catch (error) {
            //     const notify = $.notify(
            //         {message: `<b>Invalid JSON:</b><br>${error.message}`}, 
            //         {type: 'danger'}
            //     );
            //     return;
            // }
            jsonBeingEdited.value = window.CMInstance.getValue();
            jsonBeingEdited = false;
        }
        $('#modJsonEditor').modal('hide');
        window.CMInstance.toTextArea();
        window.CMInstance = null;
        discEls.cmTarget.value = '';
    });
    discEls.externalEditorBtn.addEventListener('click', (e) => {
        if(!jsonBeingEdited) return;
        const editorData = window.CMInstance.getValue();
        const leftText = (jsonBeingEdited.id === 'frmDiscord-statusEmbedJson')
            ? window.CMInstance.getValue()
            : discEls.embedJsonText.value;
        const rightText = (jsonBeingEdited.id === 'frmDiscord-statusEmbedConfigJson')
            ? window.CMInstance.getValue()
            : discEls.embedConfigJsonText.value;
        const jsonEditorUrl = `https://jsoneditoronline.org/#left=json.${encodeURIComponent(leftText)}&right=json.${encodeURIComponent(rightText)}`;
        discEls.externalEditorBtn.setAttribute('href', jsonEditorUrl);
    });

    $('#frmDiscord-save').click(function () {
        let data = {
            enabled: ($('#frmDiscord-enabled').is(':checked') === true),
            token: $('#frmDiscord-token').val().trim(),
            guild: $('#frmDiscord-guild').val().trim(),
            announceChannel: $('#frmDiscord-announceChannel').val().trim(),
            embedJson: discEls.embedJsonText.value.trim(),
            embedConfigJson: discEls.embedConfigJsonText.value.trim(),
        };
        if (data.enabled && (!data.token.length || !data.guild.length || !data.embedJson.length || !data.embedConfigJson.length)) {
            return $.notify({ message: 'The fields with "*" are required when the bot is enabled.' }, { type: 'warning' });
        }
        const { notify , progressTimerId } = startHoldingNotify('Saving');

        txAdminAPI({
            type: "POST",
            url: '/settings/save/discord',
            data: data,
            timeout: REQ_TIMEOUT_REALLY_REALLY_LONG,
            success: function (data) {
                clearInterval(progressTimerId);
                if (checkApiLogoutRefresh(data)) return;
                updateMarkdownNotification(data, notify);
            },
            error: function (xmlhttprequest, textstatus, message) {
                clearInterval(progressTimerId);
                message = (message === 'timeout')
                    ? 'The Discord API is taking too much time to respond.'
                    : `XHR Error: ${message}`;
                notify.update('progress', 0);
                notify.update('type', 'danger');
                notify.update('message', message);
            }
        });
    });


    //MARK: Save Menu Settings
    const pageKeyEl = document.getElementById('frmMenu-pageKey');
    pageKeyEl.onkeydown = (e) => {
        if (!e.metaKey) e.preventDefault();

        if(["Escape", "Backspace"].includes(e.code)){
            pageKeyEl.value = 'Tab';
        }else{
            pageKeyEl.value = e.code;
        }
    }

    $('#frmMenu-save').click(function () {
        let data = {
            menuEnabled: ($('#frmMenu-enabled').is(':checked') === true),
            menuAlignRight: ($('#frmMenu-alignRight').is(':checked') === true),
            menuPageKey: $('#frmMenu-pageKey').val().trim(),
            hideAdminInPunishments: ($('#frmMenu-hideAdminInPunishments').is(':checked') === true),
            hideAdminInMessages: ($('#frmMenu-hideAdminInMessages').is(':checked') === true),
            hideDefaultAnnouncement: ($('#frmMenu-hideDefaultAnnouncement').is(':checked') === true),
            hideDefaultDirectMessage: ($('#frmMenu-hideDefaultDirectMessage').is(':checked') === true),
            hideDefaultWarning: ($('#frmMenu-hideDefaultWarning').is(':checked') === true),
            hideDefaultScheduledRestartWarning: ($('#frmMenu-hideDefaultScheduledRestartWarning').is(':checked') === true),
        };
        const notify = $.notify({ message: '<p class="text-center">Saving...</p>' }, {});

        txAdminAPI({
            type: "POST",
            url: '/settings/save/menu',
            data: data,
            success: function (data) {
                if (checkApiLogoutRefresh(data)) return;
                updateMarkdownNotification(data, notify);
            },
            error: function (xmlhttprequest, textstatus, message) {
                notify.update('progress', 0);
                notify.update('type', 'danger');
                notify.update('message', message);
            }
        });
    });

</script>
