2025/06/29-01:46:52.431422 5b60 RocksDB version: 8.3.0
2025/06/29-01:46:52.431508 5b60 DB SUMMARY
2025/06/29-01:46:52.431532 5b60 DB Session ID:  QYUJ2XC8SFSHBM7EO5HT
2025/06/29-01:46:52.432677 5b60 CURRENT file:  CURRENT
2025/06/29-01:46:52.432696 5b60 IDENTITY file:  IDENTITY
2025/06/29-01:46:52.432776 5b60 MANIFEST file:  MANIFEST-000171 size: 496 Bytes
2025/06/29-01:46:52.432802 5b60 SST files in E:\napoly\server-data\db\default dir, Total Num: 2, files: 000166.sst 000169.sst 
2025/06/29-01:46:52.432813 5b60 Write Ahead Log file in E:\napoly\server-data\db\default: 000170.log size: 1224 ; 
2025/06/29-01:46:52.432822 5b60                         Options.error_if_exists: 0
2025/06/29-01:46:52.432831 5b60                       Options.create_if_missing: 1
2025/06/29-01:46:52.433249 5b60                         Options.paranoid_checks: 1
2025/06/29-01:46:52.433258 5b60             Options.flush_verify_memtable_count: 1
2025/06/29-01:46:52.433262 5b60                               Options.track_and_verify_wals_in_manifest: 0
2025/06/29-01:46:52.433265 5b60        Options.verify_sst_unique_id_in_manifest: 1
2025/06/29-01:46:52.433267 5b60                                     Options.env: 000002BD3E1D6650
2025/06/29-01:46:52.433271 5b60                                      Options.fs: WinFS
2025/06/29-01:46:52.433274 5b60                                Options.info_log: 000002BD493DB2E0
2025/06/29-01:46:52.433277 5b60                Options.max_file_opening_threads: 16
2025/06/29-01:46:52.433279 5b60                              Options.statistics: 0000000000000000
2025/06/29-01:46:52.433282 5b60                               Options.use_fsync: 0
2025/06/29-01:46:52.433285 5b60                       Options.max_log_file_size: 0
2025/06/29-01:46:52.433288 5b60                  Options.max_manifest_file_size: 1073741824
2025/06/29-01:46:52.433291 5b60                   Options.log_file_time_to_roll: 0
2025/06/29-01:46:52.433293 5b60                       Options.keep_log_file_num: 10
2025/06/29-01:46:52.433296 5b60                    Options.recycle_log_file_num: 0
2025/06/29-01:46:52.433299 5b60                         Options.allow_fallocate: 1
2025/06/29-01:46:52.433302 5b60                        Options.allow_mmap_reads: 0
2025/06/29-01:46:52.433305 5b60                       Options.allow_mmap_writes: 0
2025/06/29-01:46:52.433307 5b60                        Options.use_direct_reads: 0
2025/06/29-01:46:52.433310 5b60                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/29-01:46:52.433313 5b60          Options.create_missing_column_families: 0
2025/06/29-01:46:52.433316 5b60                              Options.db_log_dir: 
2025/06/29-01:46:52.433318 5b60                                 Options.wal_dir: 
2025/06/29-01:46:52.433321 5b60                Options.table_cache_numshardbits: 6
2025/06/29-01:46:52.433324 5b60                         Options.WAL_ttl_seconds: 0
2025/06/29-01:46:52.433327 5b60                       Options.WAL_size_limit_MB: 0
2025/06/29-01:46:52.433330 5b60                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/29-01:46:52.433332 5b60             Options.manifest_preallocation_size: 4194304
2025/06/29-01:46:52.433335 5b60                     Options.is_fd_close_on_exec: 1
2025/06/29-01:46:52.433338 5b60                   Options.advise_random_on_open: 1
2025/06/29-01:46:52.433341 5b60                    Options.db_write_buffer_size: 0
2025/06/29-01:46:52.433344 5b60                    Options.write_buffer_manager: 000002BD3E1D6740
2025/06/29-01:46:52.433346 5b60         Options.access_hint_on_compaction_start: 1
2025/06/29-01:46:52.433349 5b60           Options.random_access_max_buffer_size: 1048576
2025/06/29-01:46:52.433352 5b60                      Options.use_adaptive_mutex: 0
2025/06/29-01:46:52.433355 5b60                            Options.rate_limiter: 0000000000000000
2025/06/29-01:46:52.433358 5b60     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/29-01:46:52.433361 5b60                       Options.wal_recovery_mode: 2
2025/06/29-01:46:52.433363 5b60                  Options.enable_thread_tracking: 0
2025/06/29-01:46:52.433390 5b60                  Options.enable_pipelined_write: 0
2025/06/29-01:46:52.433395 5b60                  Options.unordered_write: 0
2025/06/29-01:46:52.433398 5b60         Options.allow_concurrent_memtable_write: 1
2025/06/29-01:46:52.433401 5b60      Options.enable_write_thread_adaptive_yield: 1
2025/06/29-01:46:52.433404 5b60             Options.write_thread_max_yield_usec: 100
2025/06/29-01:46:52.433407 5b60            Options.write_thread_slow_yield_usec: 3
2025/06/29-01:46:52.433409 5b60                               Options.row_cache: None
2025/06/29-01:46:52.433412 5b60                              Options.wal_filter: None
2025/06/29-01:46:52.433415 5b60             Options.avoid_flush_during_recovery: 0
2025/06/29-01:46:52.433418 5b60             Options.allow_ingest_behind: 0
2025/06/29-01:46:52.433421 5b60             Options.two_write_queues: 0
2025/06/29-01:46:52.433424 5b60             Options.manual_wal_flush: 0
2025/06/29-01:46:52.433427 5b60             Options.wal_compression: 0
2025/06/29-01:46:52.433429 5b60             Options.atomic_flush: 0
2025/06/29-01:46:52.433432 5b60             Options.avoid_unnecessary_blocking_io: 0
2025/06/29-01:46:52.433435 5b60                 Options.persist_stats_to_disk: 0
2025/06/29-01:46:52.433438 5b60                 Options.write_dbid_to_manifest: 0
2025/06/29-01:46:52.433441 5b60                 Options.log_readahead_size: 0
2025/06/29-01:46:52.433443 5b60                 Options.file_checksum_gen_factory: Unknown
2025/06/29-01:46:52.433446 5b60                 Options.best_efforts_recovery: 0
2025/06/29-01:46:52.433449 5b60                Options.max_bgerror_resume_count: 2147483647
2025/06/29-01:46:52.433452 5b60            Options.bgerror_resume_retry_interval: 1000000
2025/06/29-01:46:52.433455 5b60             Options.allow_data_in_errors: 0
2025/06/29-01:46:52.433458 5b60             Options.db_host_id: __hostname__
2025/06/29-01:46:52.433461 5b60             Options.enforce_single_del_contracts: true
2025/06/29-01:46:52.433464 5b60             Options.max_background_jobs: 2
2025/06/29-01:46:52.433467 5b60             Options.max_background_compactions: -1
2025/06/29-01:46:52.433469 5b60             Options.max_subcompactions: 1
2025/06/29-01:46:52.433472 5b60             Options.avoid_flush_during_shutdown: 0
2025/06/29-01:46:52.433475 5b60           Options.writable_file_max_buffer_size: 1048576
2025/06/29-01:46:52.433478 5b60             Options.delayed_write_rate : 16777216
2025/06/29-01:46:52.433481 5b60             Options.max_total_wal_size: 0
2025/06/29-01:46:52.433483 5b60             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/29-01:46:52.433486 5b60                   Options.stats_dump_period_sec: 600
2025/06/29-01:46:52.433489 5b60                 Options.stats_persist_period_sec: 600
2025/06/29-01:46:52.433492 5b60                 Options.stats_history_buffer_size: 1048576
2025/06/29-01:46:52.433495 5b60                          Options.max_open_files: -1
2025/06/29-01:46:52.433498 5b60                          Options.bytes_per_sync: 0
2025/06/29-01:46:52.433500 5b60                      Options.wal_bytes_per_sync: 0
2025/06/29-01:46:52.433503 5b60                   Options.strict_bytes_per_sync: 0
2025/06/29-01:46:52.433506 5b60       Options.compaction_readahead_size: 0
2025/06/29-01:46:52.433509 5b60                  Options.max_background_flushes: -1
2025/06/29-01:46:52.433512 5b60 Compression algorithms supported:
2025/06/29-01:46:52.433517 5b60 	kZSTD supported: 0
2025/06/29-01:46:52.433520 5b60 	kSnappyCompression supported: 0
2025/06/29-01:46:52.433523 5b60 	kBZip2Compression supported: 0
2025/06/29-01:46:52.433526 5b60 	kZlibCompression supported: 1
2025/06/29-01:46:52.433529 5b60 	kLZ4Compression supported: 1
2025/06/29-01:46:52.433532 5b60 	kXpressCompression supported: 0
2025/06/29-01:46:52.433535 5b60 	kLZ4HCCompression supported: 1
2025/06/29-01:46:52.433538 5b60 	kZSTDNotFinalCompression supported: 0
2025/06/29-01:46:52.433544 5b60 Fast CRC32 supported: Not supported on x86
2025/06/29-01:46:52.433562 5b60 DMutex implementation: std::mutex
2025/06/29-01:46:52.434329 5b60 [db\version_set.cc:5791] Recovering from manifest file: E:\napoly\server-data\db\default/MANIFEST-000171
2025/06/29-01:46:52.456026 5b60 [db\column_family.cc:621] --------------- Options for column family [default]:
2025/06/29-01:46:52.456043 5b60               Options.comparator: leveldb.BytewiseComparator
2025/06/29-01:46:52.456047 5b60           Options.merge_operator: None
2025/06/29-01:46:52.456050 5b60        Options.compaction_filter: None
2025/06/29-01:46:52.456053 5b60        Options.compaction_filter_factory: None
2025/06/29-01:46:52.456055 5b60  Options.sst_partitioner_factory: None
2025/06/29-01:46:52.456058 5b60         Options.memtable_factory: SkipListFactory
2025/06/29-01:46:52.456061 5b60            Options.table_factory: BlockBasedTable
2025/06/29-01:46:52.456083 5b60            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002BD4914E050)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000002BD3E1D6390
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-01:46:52.456087 5b60        Options.write_buffer_size: 67108864
2025/06/29-01:46:52.456090 5b60  Options.max_write_buffer_number: 2
2025/06/29-01:46:52.456093 5b60          Options.compression: LZ4
2025/06/29-01:46:52.456096 5b60                  Options.bottommost_compression: Disabled
2025/06/29-01:46:52.456099 5b60       Options.prefix_extractor: nullptr
2025/06/29-01:46:52.456102 5b60   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-01:46:52.456104 5b60             Options.num_levels: 7
2025/06/29-01:46:52.456107 5b60        Options.min_write_buffer_number_to_merge: 1
2025/06/29-01:46:52.456110 5b60     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-01:46:52.456113 5b60     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-01:46:52.456116 5b60            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-01:46:52.456119 5b60                  Options.bottommost_compression_opts.level: 32767
2025/06/29-01:46:52.456122 5b60               Options.bottommost_compression_opts.strategy: 0
2025/06/29-01:46:52.456124 5b60         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-01:46:52.456127 5b60         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-01:46:52.456130 5b60         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-01:46:52.456133 5b60                  Options.bottommost_compression_opts.enabled: false
2025/06/29-01:46:52.456136 5b60         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-01:46:52.456139 5b60         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-01:46:52.456142 5b60            Options.compression_opts.window_bits: -14
2025/06/29-01:46:52.456145 5b60                  Options.compression_opts.level: 32767
2025/06/29-01:46:52.456148 5b60               Options.compression_opts.strategy: 0
2025/06/29-01:46:52.456151 5b60         Options.compression_opts.max_dict_bytes: 0
2025/06/29-01:46:52.456156 5b60         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-01:46:52.456160 5b60         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-01:46:52.456163 5b60         Options.compression_opts.parallel_threads: 1
2025/06/29-01:46:52.456166 5b60                  Options.compression_opts.enabled: false
2025/06/29-01:46:52.456169 5b60         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-01:46:52.456171 5b60      Options.level0_file_num_compaction_trigger: 4
2025/06/29-01:46:52.456174 5b60          Options.level0_slowdown_writes_trigger: 20
2025/06/29-01:46:52.456177 5b60              Options.level0_stop_writes_trigger: 36
2025/06/29-01:46:52.456180 5b60                   Options.target_file_size_base: 67108864
2025/06/29-01:46:52.456183 5b60             Options.target_file_size_multiplier: 1
2025/06/29-01:46:52.456186 5b60                Options.max_bytes_for_level_base: 268435456
2025/06/29-01:46:52.456189 5b60 Options.level_compaction_dynamic_level_bytes: 0
2025/06/29-01:46:52.456191 5b60          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-01:46:52.456195 5b60 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-01:46:52.456198 5b60 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-01:46:52.456201 5b60 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-01:46:52.456204 5b60 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-01:46:52.456207 5b60 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-01:46:52.456209 5b60 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-01:46:52.456212 5b60 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-01:46:52.456215 5b60       Options.max_sequential_skip_in_iterations: 8
2025/06/29-01:46:52.456218 5b60                    Options.max_compaction_bytes: 1677721600
2025/06/29-01:46:52.456221 5b60   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-01:46:52.456224 5b60                        Options.arena_block_size: 1048576
2025/06/29-01:46:52.456227 5b60   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-01:46:52.456230 5b60   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-01:46:52.456232 5b60                Options.disable_auto_compactions: 0
2025/06/29-01:46:52.456237 5b60                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-01:46:52.456241 5b60                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-01:46:52.456244 5b60 Options.compaction_options_universal.size_ratio: 1
2025/06/29-01:46:52.456247 5b60 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-01:46:52.456249 5b60 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-01:46:52.456252 5b60 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-01:46:52.456255 5b60 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-01:46:52.456259 5b60 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-01:46:52.456262 5b60 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-01:46:52.456264 5b60 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-01:46:52.456269 5b60                   Options.table_properties_collectors: 
2025/06/29-01:46:52.456272 5b60                   Options.inplace_update_support: 0
2025/06/29-01:46:52.456275 5b60                 Options.inplace_update_num_locks: 10000
2025/06/29-01:46:52.456277 5b60               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-01:46:52.456281 5b60               Options.memtable_whole_key_filtering: 0
2025/06/29-01:46:52.456283 5b60   Options.memtable_huge_page_size: 0
2025/06/29-01:46:52.456286 5b60                           Options.bloom_locality: 0
2025/06/29-01:46:52.456289 5b60                    Options.max_successive_merges: 0
2025/06/29-01:46:52.456292 5b60                Options.optimize_filters_for_hits: 0
2025/06/29-01:46:52.456329 5b60                Options.paranoid_file_checks: 0
2025/06/29-01:46:52.456334 5b60                Options.force_consistency_checks: 1
2025/06/29-01:46:52.456337 5b60                Options.report_bg_io_stats: 0
2025/06/29-01:46:52.456340 5b60                               Options.ttl: 2592000
2025/06/29-01:46:52.456343 5b60          Options.periodic_compaction_seconds: 0
2025/06/29-01:46:52.456346 5b60  Options.preclude_last_level_data_seconds: 0
2025/06/29-01:46:52.456349 5b60    Options.preserve_internal_time_seconds: 0
2025/06/29-01:46:52.456352 5b60                       Options.enable_blob_files: false
2025/06/29-01:46:52.456355 5b60                           Options.min_blob_size: 0
2025/06/29-01:46:52.456357 5b60                          Options.blob_file_size: 268435456
2025/06/29-01:46:52.456361 5b60                   Options.blob_compression_type: NoCompression
2025/06/29-01:46:52.456364 5b60          Options.enable_blob_garbage_collection: false
2025/06/29-01:46:52.456366 5b60      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-01:46:52.456370 5b60 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-01:46:52.456373 5b60          Options.blob_compaction_readahead_size: 0
2025/06/29-01:46:52.456376 5b60                Options.blob_file_starting_level: 0
2025/06/29-01:46:52.456379 5b60 Options.experimental_mempurge_threshold: 0.000000
2025/06/29-01:46:52.470418 5b60 [db\version_set.cc:5842] Recovered from manifest file:E:\napoly\server-data\db\default/MANIFEST-000171 succeeded,manifest_file_number is 171, next_file_number is 173, last_sequence is 501, log_number is 163,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 163
2025/06/29-01:46:52.470431 5b60 [db\version_set.cc:5851] Column family [default] (ID 0), log number is 163
2025/06/29-01:46:52.477231 5b60 [db\db_impl\db_impl_open.cc:636] DB ID: 1ac3d8dc-4830-11f0-b4ab-244bfe56a7b1
2025/06/29-01:46:52.479931 5b60 EVENT_LOG_v1 {"time_micros": 1751147212479923, "job": 1, "event": "recovery_started", "wal_files": [170]}
2025/06/29-01:46:52.479952 5b60 [db\db_impl\db_impl_open.cc:1131] Recovering log #170 mode 2
2025/06/29-01:46:52.519206 5b60 EVENT_LOG_v1 {"time_micros": 1751147212519170, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 174, "file_size": 1379, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 502, "largest_seqno": 515, "table_properties": {"data_size": 351, "index_size": 74, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 951, "raw_average_key_size": 67, "raw_value_size": 77, "raw_average_value_size": 5, "num_data_blocks": 1, "num_entries": 14, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "LZ4", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751147212, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "1ac3d8dc-4830-11f0-b4ab-244bfe56a7b1", "db_session_id": "QYUJ2XC8SFSHBM7EO5HT", "orig_file_number": 174, "seqno_to_time_mapping": "N/A"}}
2025/06/29-01:46:52.538359 5b60 EVENT_LOG_v1 {"time_micros": 1751147212538351, "job": 1, "event": "recovery_finished"}
2025/06/29-01:46:52.538732 5b60 [db\version_set.cc:5304] Creating manifest 176
2025/06/29-01:46:52.618707 5b60 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000170.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-01:46:52.618738 5b60 [db\db_impl\db_impl_files.cc:654] [JOB 2] Delete info log file E:\napoly\server-data\db\default//LOG.old.1750000604564197
2025/06/29-01:46:52.619078 5b60 [db\db_impl\db_impl_open.cc:2085] SstFileManager instance 000002BD46E49290
2025/06/29-01:46:52.620414 5b60 DB pointer 000002BD490B9040
2025/06/29-01:46:52.621021 4454 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-01:46:52.621032 4454 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 0.2 total, 0.2 interval
Cumulative writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.69 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.039       0      0       0.0       0.0
  L1      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    4.04 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.039       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.039       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.04              0.00         1    0.039       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.2 total, 0.2 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002BD3E1D6390#7320 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 1 last_copies: 0 last_secs: 4.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/29-01:56:52.621552 4454 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-01:56:52.621583 4454 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 600.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 13 writes, 13 keys, 13 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.69 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.039       0      0       0.0       0.0
  L1      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    4.04 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.039       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.04              0.00         1    0.039       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 600.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002BD3E1D6390#7320 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 2 last_copies: 0 last_secs: 6.3e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
