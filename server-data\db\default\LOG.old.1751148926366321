2025/06/29-02:01:34.362373 51c0 RocksDB version: 8.3.0
2025/06/29-02:01:34.362467 51c0 DB SUMMARY
2025/06/29-02:01:34.362493 51c0 DB Session ID:  U1P59H25C6JMQBLEFN16
2025/06/29-02:01:34.364411 51c0 CURRENT file:  CURRENT
2025/06/29-02:01:34.364456 51c0 IDENTITY file:  IDENTITY
2025/06/29-02:01:34.364563 51c0 MANIFEST file:  MANIFEST-000176 size: 681 Bytes
2025/06/29-02:01:34.364585 51c0 SST files in E:\napoly\server-data\db\default dir, Total Num: 3, files: 000166.sst 000169.sst 000174.sst 
2025/06/29-02:01:34.364600 51c0 Write Ahead Log file in E:\napoly\server-data\db\default: 000175.log size: 1224 ; 
2025/06/29-02:01:34.364615 51c0                         Options.error_if_exists: 0
2025/06/29-02:01:34.364629 51c0                       Options.create_if_missing: 1
2025/06/29-02:01:34.365059 51c0                         Options.paranoid_checks: 1
2025/06/29-02:01:34.365072 51c0             Options.flush_verify_memtable_count: 1
2025/06/29-02:01:34.365076 51c0                               Options.track_and_verify_wals_in_manifest: 0
2025/06/29-02:01:34.365080 51c0        Options.verify_sst_unique_id_in_manifest: 1
2025/06/29-02:01:34.365085 51c0                                     Options.env: 00000247A41EC170
2025/06/29-02:01:34.365090 51c0                                      Options.fs: WinFS
2025/06/29-02:01:34.365094 51c0                                Options.info_log: 00000247B99C47A0
2025/06/29-02:01:34.365099 51c0                Options.max_file_opening_threads: 16
2025/06/29-02:01:34.365103 51c0                              Options.statistics: 0000000000000000
2025/06/29-02:01:34.365108 51c0                               Options.use_fsync: 0
2025/06/29-02:01:34.365112 51c0                       Options.max_log_file_size: 0
2025/06/29-02:01:34.365117 51c0                  Options.max_manifest_file_size: 1073741824
2025/06/29-02:01:34.365122 51c0                   Options.log_file_time_to_roll: 0
2025/06/29-02:01:34.365126 51c0                       Options.keep_log_file_num: 10
2025/06/29-02:01:34.365130 51c0                    Options.recycle_log_file_num: 0
2025/06/29-02:01:34.365135 51c0                         Options.allow_fallocate: 1
2025/06/29-02:01:34.365139 51c0                        Options.allow_mmap_reads: 0
2025/06/29-02:01:34.365143 51c0                       Options.allow_mmap_writes: 0
2025/06/29-02:01:34.365148 51c0                        Options.use_direct_reads: 0
2025/06/29-02:01:34.365152 51c0                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/29-02:01:34.365156 51c0          Options.create_missing_column_families: 0
2025/06/29-02:01:34.365161 51c0                              Options.db_log_dir: 
2025/06/29-02:01:34.365165 51c0                                 Options.wal_dir: 
2025/06/29-02:01:34.365170 51c0                Options.table_cache_numshardbits: 6
2025/06/29-02:01:34.365175 51c0                         Options.WAL_ttl_seconds: 0
2025/06/29-02:01:34.365179 51c0                       Options.WAL_size_limit_MB: 0
2025/06/29-02:01:34.365184 51c0                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/29-02:01:34.365188 51c0             Options.manifest_preallocation_size: 4194304
2025/06/29-02:01:34.365192 51c0                     Options.is_fd_close_on_exec: 1
2025/06/29-02:01:34.365197 51c0                   Options.advise_random_on_open: 1
2025/06/29-02:01:34.365201 51c0                    Options.db_write_buffer_size: 0
2025/06/29-02:01:34.365205 51c0                    Options.write_buffer_manager: 00000247A41EC350
2025/06/29-02:01:34.365210 51c0         Options.access_hint_on_compaction_start: 1
2025/06/29-02:01:34.365214 51c0           Options.random_access_max_buffer_size: 1048576
2025/06/29-02:01:34.365219 51c0                      Options.use_adaptive_mutex: 0
2025/06/29-02:01:34.365224 51c0                            Options.rate_limiter: 0000000000000000
2025/06/29-02:01:34.365229 51c0     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/29-02:01:34.365233 51c0                       Options.wal_recovery_mode: 2
2025/06/29-02:01:34.365270 51c0                  Options.enable_thread_tracking: 0
2025/06/29-02:01:34.365279 51c0                  Options.enable_pipelined_write: 0
2025/06/29-02:01:34.365283 51c0                  Options.unordered_write: 0
2025/06/29-02:01:34.365288 51c0         Options.allow_concurrent_memtable_write: 1
2025/06/29-02:01:34.365292 51c0      Options.enable_write_thread_adaptive_yield: 1
2025/06/29-02:01:34.365296 51c0             Options.write_thread_max_yield_usec: 100
2025/06/29-02:01:34.365300 51c0            Options.write_thread_slow_yield_usec: 3
2025/06/29-02:01:34.365305 51c0                               Options.row_cache: None
2025/06/29-02:01:34.365309 51c0                              Options.wal_filter: None
2025/06/29-02:01:34.365314 51c0             Options.avoid_flush_during_recovery: 0
2025/06/29-02:01:34.365319 51c0             Options.allow_ingest_behind: 0
2025/06/29-02:01:34.365323 51c0             Options.two_write_queues: 0
2025/06/29-02:01:34.365328 51c0             Options.manual_wal_flush: 0
2025/06/29-02:01:34.365332 51c0             Options.wal_compression: 0
2025/06/29-02:01:34.365336 51c0             Options.atomic_flush: 0
2025/06/29-02:01:34.365340 51c0             Options.avoid_unnecessary_blocking_io: 0
2025/06/29-02:01:34.365345 51c0                 Options.persist_stats_to_disk: 0
2025/06/29-02:01:34.365349 51c0                 Options.write_dbid_to_manifest: 0
2025/06/29-02:01:34.365353 51c0                 Options.log_readahead_size: 0
2025/06/29-02:01:34.365358 51c0                 Options.file_checksum_gen_factory: Unknown
2025/06/29-02:01:34.365362 51c0                 Options.best_efforts_recovery: 0
2025/06/29-02:01:34.365366 51c0                Options.max_bgerror_resume_count: 2147483647
2025/06/29-02:01:34.365371 51c0            Options.bgerror_resume_retry_interval: 1000000
2025/06/29-02:01:34.365375 51c0             Options.allow_data_in_errors: 0
2025/06/29-02:01:34.365379 51c0             Options.db_host_id: __hostname__
2025/06/29-02:01:34.365383 51c0             Options.enforce_single_del_contracts: true
2025/06/29-02:01:34.365388 51c0             Options.max_background_jobs: 2
2025/06/29-02:01:34.365392 51c0             Options.max_background_compactions: -1
2025/06/29-02:01:34.365397 51c0             Options.max_subcompactions: 1
2025/06/29-02:01:34.365400 51c0             Options.avoid_flush_during_shutdown: 0
2025/06/29-02:01:34.365404 51c0           Options.writable_file_max_buffer_size: 1048576
2025/06/29-02:01:34.365407 51c0             Options.delayed_write_rate : 16777216
2025/06/29-02:01:34.365411 51c0             Options.max_total_wal_size: 0
2025/06/29-02:01:34.365415 51c0             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/29-02:01:34.365419 51c0                   Options.stats_dump_period_sec: 600
2025/06/29-02:01:34.365422 51c0                 Options.stats_persist_period_sec: 600
2025/06/29-02:01:34.365426 51c0                 Options.stats_history_buffer_size: 1048576
2025/06/29-02:01:34.365430 51c0                          Options.max_open_files: -1
2025/06/29-02:01:34.365433 51c0                          Options.bytes_per_sync: 0
2025/06/29-02:01:34.365437 51c0                      Options.wal_bytes_per_sync: 0
2025/06/29-02:01:34.365441 51c0                   Options.strict_bytes_per_sync: 0
2025/06/29-02:01:34.365444 51c0       Options.compaction_readahead_size: 0
2025/06/29-02:01:34.365448 51c0                  Options.max_background_flushes: -1
2025/06/29-02:01:34.365452 51c0 Compression algorithms supported:
2025/06/29-02:01:34.365461 51c0 	kZSTD supported: 0
2025/06/29-02:01:34.365465 51c0 	kSnappyCompression supported: 0
2025/06/29-02:01:34.365469 51c0 	kBZip2Compression supported: 0
2025/06/29-02:01:34.365473 51c0 	kZlibCompression supported: 1
2025/06/29-02:01:34.365476 51c0 	kLZ4Compression supported: 1
2025/06/29-02:01:34.365480 51c0 	kXpressCompression supported: 0
2025/06/29-02:01:34.365484 51c0 	kLZ4HCCompression supported: 1
2025/06/29-02:01:34.365489 51c0 	kZSTDNotFinalCompression supported: 0
2025/06/29-02:01:34.365527 51c0 Fast CRC32 supported: Not supported on x86
2025/06/29-02:01:34.365534 51c0 DMutex implementation: std::mutex
2025/06/29-02:01:34.366862 51c0 [db\version_set.cc:5791] Recovering from manifest file: E:\napoly\server-data\db\default/MANIFEST-000176
2025/06/29-02:01:34.367170 51c0 [db\column_family.cc:621] --------------- Options for column family [default]:
2025/06/29-02:01:34.367182 51c0               Options.comparator: leveldb.BytewiseComparator
2025/06/29-02:01:34.367187 51c0           Options.merge_operator: None
2025/06/29-02:01:34.367192 51c0        Options.compaction_filter: None
2025/06/29-02:01:34.367195 51c0        Options.compaction_filter_factory: None
2025/06/29-02:01:34.367199 51c0  Options.sst_partitioner_factory: None
2025/06/29-02:01:34.367203 51c0         Options.memtable_factory: SkipListFactory
2025/06/29-02:01:34.367208 51c0            Options.table_factory: BlockBasedTable
2025/06/29-02:01:34.367258 51c0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000247BBBDBD60)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000247A41EC270
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-02:01:34.367265 51c0        Options.write_buffer_size: 67108864
2025/06/29-02:01:34.367269 51c0  Options.max_write_buffer_number: 2
2025/06/29-02:01:34.367274 51c0          Options.compression: LZ4
2025/06/29-02:01:34.367278 51c0                  Options.bottommost_compression: Disabled
2025/06/29-02:01:34.367282 51c0       Options.prefix_extractor: nullptr
2025/06/29-02:01:34.367286 51c0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-02:01:34.367290 51c0             Options.num_levels: 7
2025/06/29-02:01:34.367294 51c0        Options.min_write_buffer_number_to_merge: 1
2025/06/29-02:01:34.367298 51c0     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-02:01:34.367302 51c0     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-02:01:34.367306 51c0            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-02:01:34.367311 51c0                  Options.bottommost_compression_opts.level: 32767
2025/06/29-02:01:34.367315 51c0               Options.bottommost_compression_opts.strategy: 0
2025/06/29-02:01:34.367319 51c0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-02:01:34.367323 51c0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-02:01:34.367327 51c0         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-02:01:34.367330 51c0                  Options.bottommost_compression_opts.enabled: false
2025/06/29-02:01:34.367335 51c0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-02:01:34.367339 51c0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-02:01:34.367343 51c0            Options.compression_opts.window_bits: -14
2025/06/29-02:01:34.367347 51c0                  Options.compression_opts.level: 32767
2025/06/29-02:01:34.367351 51c0               Options.compression_opts.strategy: 0
2025/06/29-02:01:34.367354 51c0         Options.compression_opts.max_dict_bytes: 0
2025/06/29-02:01:34.367364 51c0         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-02:01:34.367369 51c0         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-02:01:34.367374 51c0         Options.compression_opts.parallel_threads: 1
2025/06/29-02:01:34.367378 51c0                  Options.compression_opts.enabled: false
2025/06/29-02:01:34.367382 51c0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-02:01:34.367386 51c0      Options.level0_file_num_compaction_trigger: 4
2025/06/29-02:01:34.367390 51c0          Options.level0_slowdown_writes_trigger: 20
2025/06/29-02:01:34.367394 51c0              Options.level0_stop_writes_trigger: 36
2025/06/29-02:01:34.367398 51c0                   Options.target_file_size_base: 67108864
2025/06/29-02:01:34.367402 51c0             Options.target_file_size_multiplier: 1
2025/06/29-02:01:34.367406 51c0                Options.max_bytes_for_level_base: 268435456
2025/06/29-02:01:34.367410 51c0 Options.level_compaction_dynamic_level_bytes: 0
2025/06/29-02:01:34.367413 51c0          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-02:01:34.367419 51c0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-02:01:34.367423 51c0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-02:01:34.367427 51c0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-02:01:34.367431 51c0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-02:01:34.367435 51c0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-02:01:34.367439 51c0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-02:01:34.367442 51c0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-02:01:34.367447 51c0       Options.max_sequential_skip_in_iterations: 8
2025/06/29-02:01:34.367451 51c0                    Options.max_compaction_bytes: 1677721600
2025/06/29-02:01:34.367454 51c0   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-02:01:34.367458 51c0                        Options.arena_block_size: 1048576
2025/06/29-02:01:34.367463 51c0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-02:01:34.367467 51c0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-02:01:34.367471 51c0                Options.disable_auto_compactions: 0
2025/06/29-02:01:34.367479 51c0                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-02:01:34.367485 51c0                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-02:01:34.367490 51c0 Options.compaction_options_universal.size_ratio: 1
2025/06/29-02:01:34.367494 51c0 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-02:01:34.367498 51c0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-02:01:34.367502 51c0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-02:01:34.367506 51c0 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-02:01:34.367511 51c0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-02:01:34.367515 51c0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-02:01:34.367519 51c0 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-02:01:34.367527 51c0                   Options.table_properties_collectors: 
2025/06/29-02:01:34.367531 51c0                   Options.inplace_update_support: 0
2025/06/29-02:01:34.367536 51c0                 Options.inplace_update_num_locks: 10000
2025/06/29-02:01:34.367540 51c0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-02:01:34.367545 51c0               Options.memtable_whole_key_filtering: 0
2025/06/29-02:01:34.367549 51c0   Options.memtable_huge_page_size: 0
2025/06/29-02:01:34.367553 51c0                           Options.bloom_locality: 0
2025/06/29-02:01:34.367556 51c0                    Options.max_successive_merges: 0
2025/06/29-02:01:34.367560 51c0                Options.optimize_filters_for_hits: 0
2025/06/29-02:01:34.367626 51c0                Options.paranoid_file_checks: 0
2025/06/29-02:01:34.367633 51c0                Options.force_consistency_checks: 1
2025/06/29-02:01:34.367637 51c0                Options.report_bg_io_stats: 0
2025/06/29-02:01:34.367641 51c0                               Options.ttl: 2592000
2025/06/29-02:01:34.367645 51c0          Options.periodic_compaction_seconds: 0
2025/06/29-02:01:34.367649 51c0  Options.preclude_last_level_data_seconds: 0
2025/06/29-02:01:34.367653 51c0    Options.preserve_internal_time_seconds: 0
2025/06/29-02:01:34.367656 51c0                       Options.enable_blob_files: false
2025/06/29-02:01:34.367660 51c0                           Options.min_blob_size: 0
2025/06/29-02:01:34.367664 51c0                          Options.blob_file_size: 268435456
2025/06/29-02:01:34.367668 51c0                   Options.blob_compression_type: NoCompression
2025/06/29-02:01:34.367672 51c0          Options.enable_blob_garbage_collection: false
2025/06/29-02:01:34.367676 51c0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-02:01:34.367681 51c0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-02:01:34.367685 51c0          Options.blob_compaction_readahead_size: 0
2025/06/29-02:01:34.367689 51c0                Options.blob_file_starting_level: 0
2025/06/29-02:01:34.367693 51c0 Options.experimental_mempurge_threshold: 0.000000
2025/06/29-02:01:34.371103 51c0 [db\version_set.cc:5842] Recovered from manifest file:E:\napoly\server-data\db\default/MANIFEST-000176 succeeded,manifest_file_number is 176, next_file_number is 178, last_sequence is 515, log_number is 171,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 171
2025/06/29-02:01:34.371133 51c0 [db\version_set.cc:5851] Column family [default] (ID 0), log number is 171
2025/06/29-02:01:34.371574 51c0 [db\db_impl\db_impl_open.cc:636] DB ID: 1ac3d8dc-4830-11f0-b4ab-244bfe56a7b1
2025/06/29-02:01:34.376117 51c0 EVENT_LOG_v1 {"time_micros": 1751148094376105, "job": 1, "event": "recovery_started", "wal_files": [175]}
2025/06/29-02:01:34.376144 51c0 [db\db_impl\db_impl_open.cc:1131] Recovering log #175 mode 2
2025/06/29-02:01:34.483634 51c0 EVENT_LOG_v1 {"time_micros": 1751148094483580, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 179, "file_size": 1375, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 516, "largest_seqno": 529, "table_properties": {"data_size": 347, "index_size": 74, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 951, "raw_average_key_size": 67, "raw_value_size": 77, "raw_average_value_size": 5, "num_data_blocks": 1, "num_entries": 14, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "LZ4", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751148094, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "1ac3d8dc-4830-11f0-b4ab-244bfe56a7b1", "db_session_id": "U1P59H25C6JMQBLEFN16", "orig_file_number": 179, "seqno_to_time_mapping": "N/A"}}
2025/06/29-02:01:34.509353 51c0 EVENT_LOG_v1 {"time_micros": 1751148094509337, "job": 1, "event": "recovery_finished"}
2025/06/29-02:01:34.510059 51c0 [db\version_set.cc:5304] Creating manifest 181
2025/06/29-02:01:34.617798 51c0 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000175.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-02:01:34.617875 51c0 [db\db_impl\db_impl_files.cc:654] [JOB 2] Delete info log file E:\napoly\server-data\db\default//LOG.old.1750002211896380
2025/06/29-02:01:34.618145 51c0 [db\db_impl\db_impl_open.cc:2085] SstFileManager instance 00000247C262E9F0
2025/06/29-02:01:34.620069 51c0 DB pointer 00000247BF9AF080
2025/06/29-02:01:34.620705 4b1c [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-02:01:34.620723 4b1c [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 0.3 total, 0.3 interval
Cumulative writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    4.03 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.11              0.00         1    0.107       0      0       0.0       0.0
  L1      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.38 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.11              0.00         1    0.107       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.11              0.00         1    0.107       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.11              0.00         1    0.107       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.3 total, 0.3 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000247A41EC270#5348 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 1 last_copies: 0 last_secs: 6.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/29-02:11:34.622095 4b1c [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-02:11:34.622135 4b1c [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 600.3 total, 600.0 interval
Cumulative writes: 28 writes, 28 keys, 28 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 28 writes, 28 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 27 writes, 27 keys, 27 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 28 writes, 28 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    4.03 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.11              0.00         1    0.107       0      0       0.0       0.0
  L1      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.38 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.11              0.00         1    0.107       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.11              0.00         1    0.107       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 600.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000247A41EC270#5348 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 2 last_copies: 0 last_secs: 9.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
