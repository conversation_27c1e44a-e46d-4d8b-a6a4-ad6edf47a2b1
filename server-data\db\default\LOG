2025/06/29-02:58:54.383410 25b4 RocksDB version: 8.3.0
2025/06/29-02:58:54.383497 25b4 DB SUMMARY
2025/06/29-02:58:54.383520 25b4 DB Session ID:  AOHDQY4T2OB83BGOXDQM
2025/06/29-02:58:54.385348 25b4 CURRENT file:  CURRENT
2025/06/29-02:58:54.385388 25b4 IDENTITY file:  IDENTITY
2025/06/29-02:58:54.385482 25b4 MANIFEST file:  MANIFEST-000199 size: 681 Bytes
2025/06/29-02:58:54.385501 25b4 SST files in E:\napoly\server-data\db\default dir, Total Num: 3, files: 000189.sst 000192.sst 000197.sst 
2025/06/29-02:58:54.385515 25b4 Write Ahead Log file in E:\napoly\server-data\db\default: 000198.log size: 2448 ; 
2025/06/29-02:58:54.385529 25b4                         Options.error_if_exists: 0
2025/06/29-02:58:54.385542 25b4                       Options.create_if_missing: 1
2025/06/29-02:58:54.385993 25b4                         Options.paranoid_checks: 1
2025/06/29-02:58:54.386007 25b4             Options.flush_verify_memtable_count: 1
2025/06/29-02:58:54.386011 25b4                               Options.track_and_verify_wals_in_manifest: 0
2025/06/29-02:58:54.386015 25b4        Options.verify_sst_unique_id_in_manifest: 1
2025/06/29-02:58:54.386019 25b4                                     Options.env: 00000240A77074B0
2025/06/29-02:58:54.386024 25b4                                      Options.fs: WinFS
2025/06/29-02:58:54.386028 25b4                                Options.info_log: 00000240A9BEAAA0
2025/06/29-02:58:54.386032 25b4                Options.max_file_opening_threads: 16
2025/06/29-02:58:54.386036 25b4                              Options.statistics: 0000000000000000
2025/06/29-02:58:54.386040 25b4                               Options.use_fsync: 0
2025/06/29-02:58:54.386044 25b4                       Options.max_log_file_size: 0
2025/06/29-02:58:54.386049 25b4                  Options.max_manifest_file_size: 1073741824
2025/06/29-02:58:54.386053 25b4                   Options.log_file_time_to_roll: 0
2025/06/29-02:58:54.386056 25b4                       Options.keep_log_file_num: 10
2025/06/29-02:58:54.386060 25b4                    Options.recycle_log_file_num: 0
2025/06/29-02:58:54.386064 25b4                         Options.allow_fallocate: 1
2025/06/29-02:58:54.386068 25b4                        Options.allow_mmap_reads: 0
2025/06/29-02:58:54.386073 25b4                       Options.allow_mmap_writes: 0
2025/06/29-02:58:54.386076 25b4                        Options.use_direct_reads: 0
2025/06/29-02:58:54.386080 25b4                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/29-02:58:54.386085 25b4          Options.create_missing_column_families: 0
2025/06/29-02:58:54.386088 25b4                              Options.db_log_dir: 
2025/06/29-02:58:54.386092 25b4                                 Options.wal_dir: 
2025/06/29-02:58:54.386097 25b4                Options.table_cache_numshardbits: 6
2025/06/29-02:58:54.386101 25b4                         Options.WAL_ttl_seconds: 0
2025/06/29-02:58:54.386105 25b4                       Options.WAL_size_limit_MB: 0
2025/06/29-02:58:54.386109 25b4                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/29-02:58:54.386113 25b4             Options.manifest_preallocation_size: 4194304
2025/06/29-02:58:54.386117 25b4                     Options.is_fd_close_on_exec: 1
2025/06/29-02:58:54.386121 25b4                   Options.advise_random_on_open: 1
2025/06/29-02:58:54.386125 25b4                    Options.db_write_buffer_size: 0
2025/06/29-02:58:54.386129 25b4                    Options.write_buffer_manager: 00000240A7709670
2025/06/29-02:58:54.386133 25b4         Options.access_hint_on_compaction_start: 1
2025/06/29-02:58:54.386137 25b4           Options.random_access_max_buffer_size: 1048576
2025/06/29-02:58:54.386141 25b4                      Options.use_adaptive_mutex: 0
2025/06/29-02:58:54.386145 25b4                            Options.rate_limiter: 0000000000000000
2025/06/29-02:58:54.386150 25b4     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/29-02:58:54.386154 25b4                       Options.wal_recovery_mode: 2
2025/06/29-02:58:54.386194 25b4                  Options.enable_thread_tracking: 0
2025/06/29-02:58:54.386202 25b4                  Options.enable_pipelined_write: 0
2025/06/29-02:58:54.386206 25b4                  Options.unordered_write: 0
2025/06/29-02:58:54.386210 25b4         Options.allow_concurrent_memtable_write: 1
2025/06/29-02:58:54.386214 25b4      Options.enable_write_thread_adaptive_yield: 1
2025/06/29-02:58:54.386218 25b4             Options.write_thread_max_yield_usec: 100
2025/06/29-02:58:54.386222 25b4            Options.write_thread_slow_yield_usec: 3
2025/06/29-02:58:54.386226 25b4                               Options.row_cache: None
2025/06/29-02:58:54.386229 25b4                              Options.wal_filter: None
2025/06/29-02:58:54.386234 25b4             Options.avoid_flush_during_recovery: 0
2025/06/29-02:58:54.386238 25b4             Options.allow_ingest_behind: 0
2025/06/29-02:58:54.386241 25b4             Options.two_write_queues: 0
2025/06/29-02:58:54.386245 25b4             Options.manual_wal_flush: 0
2025/06/29-02:58:54.386249 25b4             Options.wal_compression: 0
2025/06/29-02:58:54.386253 25b4             Options.atomic_flush: 0
2025/06/29-02:58:54.386256 25b4             Options.avoid_unnecessary_blocking_io: 0
2025/06/29-02:58:54.386260 25b4                 Options.persist_stats_to_disk: 0
2025/06/29-02:58:54.386264 25b4                 Options.write_dbid_to_manifest: 0
2025/06/29-02:58:54.386268 25b4                 Options.log_readahead_size: 0
2025/06/29-02:58:54.386273 25b4                 Options.file_checksum_gen_factory: Unknown
2025/06/29-02:58:54.386277 25b4                 Options.best_efforts_recovery: 0
2025/06/29-02:58:54.386281 25b4                Options.max_bgerror_resume_count: 2147483647
2025/06/29-02:58:54.386284 25b4            Options.bgerror_resume_retry_interval: 1000000
2025/06/29-02:58:54.386288 25b4             Options.allow_data_in_errors: 0
2025/06/29-02:58:54.386292 25b4             Options.db_host_id: __hostname__
2025/06/29-02:58:54.386296 25b4             Options.enforce_single_del_contracts: true
2025/06/29-02:58:54.386300 25b4             Options.max_background_jobs: 2
2025/06/29-02:58:54.386304 25b4             Options.max_background_compactions: -1
2025/06/29-02:58:54.386308 25b4             Options.max_subcompactions: 1
2025/06/29-02:58:54.386312 25b4             Options.avoid_flush_during_shutdown: 0
2025/06/29-02:58:54.386315 25b4           Options.writable_file_max_buffer_size: 1048576
2025/06/29-02:58:54.386320 25b4             Options.delayed_write_rate : 16777216
2025/06/29-02:58:54.386324 25b4             Options.max_total_wal_size: 0
2025/06/29-02:58:54.386327 25b4             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/29-02:58:54.386331 25b4                   Options.stats_dump_period_sec: 600
2025/06/29-02:58:54.386335 25b4                 Options.stats_persist_period_sec: 600
2025/06/29-02:58:54.386339 25b4                 Options.stats_history_buffer_size: 1048576
2025/06/29-02:58:54.386343 25b4                          Options.max_open_files: -1
2025/06/29-02:58:54.386347 25b4                          Options.bytes_per_sync: 0
2025/06/29-02:58:54.386351 25b4                      Options.wal_bytes_per_sync: 0
2025/06/29-02:58:54.386355 25b4                   Options.strict_bytes_per_sync: 0
2025/06/29-02:58:54.386358 25b4       Options.compaction_readahead_size: 0
2025/06/29-02:58:54.386362 25b4                  Options.max_background_flushes: -1
2025/06/29-02:58:54.386366 25b4 Compression algorithms supported:
2025/06/29-02:58:54.386375 25b4 	kZSTD supported: 0
2025/06/29-02:58:54.386379 25b4 	kSnappyCompression supported: 0
2025/06/29-02:58:54.386383 25b4 	kBZip2Compression supported: 0
2025/06/29-02:58:54.386388 25b4 	kZlibCompression supported: 1
2025/06/29-02:58:54.386391 25b4 	kLZ4Compression supported: 1
2025/06/29-02:58:54.386395 25b4 	kXpressCompression supported: 0
2025/06/29-02:58:54.386400 25b4 	kLZ4HCCompression supported: 1
2025/06/29-02:58:54.386403 25b4 	kZSTDNotFinalCompression supported: 0
2025/06/29-02:58:54.386439 25b4 Fast CRC32 supported: Not supported on x86
2025/06/29-02:58:54.386447 25b4 DMutex implementation: std::mutex
2025/06/29-02:58:54.387990 25b4 [db\version_set.cc:5791] Recovering from manifest file: E:\napoly\server-data\db\default/MANIFEST-000199
2025/06/29-02:58:54.388282 25b4 [db\column_family.cc:621] --------------- Options for column family [default]:
2025/06/29-02:58:54.388293 25b4               Options.comparator: leveldb.BytewiseComparator
2025/06/29-02:58:54.388298 25b4           Options.merge_operator: None
2025/06/29-02:58:54.388303 25b4        Options.compaction_filter: None
2025/06/29-02:58:54.388307 25b4        Options.compaction_filter_factory: None
2025/06/29-02:58:54.388311 25b4  Options.sst_partitioner_factory: None
2025/06/29-02:58:54.388315 25b4         Options.memtable_factory: SkipListFactory
2025/06/29-02:58:54.388319 25b4            Options.table_factory: BlockBasedTable
2025/06/29-02:58:54.388351 25b4            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000240AA1187F0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000240A77093B0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-02:58:54.388356 25b4        Options.write_buffer_size: 67108864
2025/06/29-02:58:54.388361 25b4  Options.max_write_buffer_number: 2
2025/06/29-02:58:54.388365 25b4          Options.compression: LZ4
2025/06/29-02:58:54.388369 25b4                  Options.bottommost_compression: Disabled
2025/06/29-02:58:54.388373 25b4       Options.prefix_extractor: nullptr
2025/06/29-02:58:54.388378 25b4   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-02:58:54.388382 25b4             Options.num_levels: 7
2025/06/29-02:58:54.388386 25b4        Options.min_write_buffer_number_to_merge: 1
2025/06/29-02:58:54.388391 25b4     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-02:58:54.388395 25b4     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-02:58:54.388399 25b4            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-02:58:54.388404 25b4                  Options.bottommost_compression_opts.level: 32767
2025/06/29-02:58:54.388408 25b4               Options.bottommost_compression_opts.strategy: 0
2025/06/29-02:58:54.388412 25b4         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-02:58:54.388416 25b4         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-02:58:54.388421 25b4         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-02:58:54.388425 25b4                  Options.bottommost_compression_opts.enabled: false
2025/06/29-02:58:54.388429 25b4         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-02:58:54.388434 25b4         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-02:58:54.388438 25b4            Options.compression_opts.window_bits: -14
2025/06/29-02:58:54.388443 25b4                  Options.compression_opts.level: 32767
2025/06/29-02:58:54.388447 25b4               Options.compression_opts.strategy: 0
2025/06/29-02:58:54.388451 25b4         Options.compression_opts.max_dict_bytes: 0
2025/06/29-02:58:54.388459 25b4         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-02:58:54.388465 25b4         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-02:58:54.388469 25b4         Options.compression_opts.parallel_threads: 1
2025/06/29-02:58:54.388473 25b4                  Options.compression_opts.enabled: false
2025/06/29-02:58:54.388477 25b4         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-02:58:54.388482 25b4      Options.level0_file_num_compaction_trigger: 4
2025/06/29-02:58:54.388486 25b4          Options.level0_slowdown_writes_trigger: 20
2025/06/29-02:58:54.388490 25b4              Options.level0_stop_writes_trigger: 36
2025/06/29-02:58:54.388495 25b4                   Options.target_file_size_base: 67108864
2025/06/29-02:58:54.388499 25b4             Options.target_file_size_multiplier: 1
2025/06/29-02:58:54.388503 25b4                Options.max_bytes_for_level_base: 268435456
2025/06/29-02:58:54.388508 25b4 Options.level_compaction_dynamic_level_bytes: 0
2025/06/29-02:58:54.388512 25b4          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-02:58:54.388517 25b4 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-02:58:54.388522 25b4 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-02:58:54.388526 25b4 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-02:58:54.388530 25b4 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-02:58:54.388535 25b4 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-02:58:54.388539 25b4 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-02:58:54.388543 25b4 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-02:58:54.388548 25b4       Options.max_sequential_skip_in_iterations: 8
2025/06/29-02:58:54.388552 25b4                    Options.max_compaction_bytes: 1677721600
2025/06/29-02:58:54.388557 25b4   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-02:58:54.388561 25b4                        Options.arena_block_size: 1048576
2025/06/29-02:58:54.388565 25b4   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-02:58:54.388570 25b4   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-02:58:54.388574 25b4                Options.disable_auto_compactions: 0
2025/06/29-02:58:54.388582 25b4                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-02:58:54.388586 25b4                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-02:58:54.388590 25b4 Options.compaction_options_universal.size_ratio: 1
2025/06/29-02:58:54.388595 25b4 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-02:58:54.388599 25b4 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-02:58:54.388603 25b4 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-02:58:54.388607 25b4 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-02:58:54.388612 25b4 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-02:58:54.388616 25b4 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-02:58:54.388621 25b4 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-02:58:54.388626 25b4                   Options.table_properties_collectors: 
2025/06/29-02:58:54.388630 25b4                   Options.inplace_update_support: 0
2025/06/29-02:58:54.388634 25b4                 Options.inplace_update_num_locks: 10000
2025/06/29-02:58:54.388638 25b4               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-02:58:54.388643 25b4               Options.memtable_whole_key_filtering: 0
2025/06/29-02:58:54.388647 25b4   Options.memtable_huge_page_size: 0
2025/06/29-02:58:54.388651 25b4                           Options.bloom_locality: 0
2025/06/29-02:58:54.388655 25b4                    Options.max_successive_merges: 0
2025/06/29-02:58:54.388659 25b4                Options.optimize_filters_for_hits: 0
2025/06/29-02:58:54.388704 25b4                Options.paranoid_file_checks: 0
2025/06/29-02:58:54.388710 25b4                Options.force_consistency_checks: 1
2025/06/29-02:58:54.388715 25b4                Options.report_bg_io_stats: 0
2025/06/29-02:58:54.388719 25b4                               Options.ttl: 2592000
2025/06/29-02:58:54.388722 25b4          Options.periodic_compaction_seconds: 0
2025/06/29-02:58:54.388727 25b4  Options.preclude_last_level_data_seconds: 0
2025/06/29-02:58:54.388731 25b4    Options.preserve_internal_time_seconds: 0
2025/06/29-02:58:54.388734 25b4                       Options.enable_blob_files: false
2025/06/29-02:58:54.388738 25b4                           Options.min_blob_size: 0
2025/06/29-02:58:54.388742 25b4                          Options.blob_file_size: 268435456
2025/06/29-02:58:54.388746 25b4                   Options.blob_compression_type: NoCompression
2025/06/29-02:58:54.388750 25b4          Options.enable_blob_garbage_collection: false
2025/06/29-02:58:54.388754 25b4      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-02:58:54.388770 25b4 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-02:58:54.388776 25b4          Options.blob_compaction_readahead_size: 0
2025/06/29-02:58:54.388780 25b4                Options.blob_file_starting_level: 0
2025/06/29-02:58:54.388783 25b4 Options.experimental_mempurge_threshold: 0.000000
2025/06/29-02:58:54.391780 25b4 [db\version_set.cc:5842] Recovered from manifest file:E:\napoly\server-data\db\default/MANIFEST-000199 succeeded,manifest_file_number is 199, next_file_number is 201, last_sequence is 585, log_number is 194,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 194
2025/06/29-02:58:54.391805 25b4 [db\version_set.cc:5851] Column family [default] (ID 0), log number is 194
2025/06/29-02:58:54.392250 25b4 [db\db_impl\db_impl_open.cc:636] DB ID: 1ac3d8dc-4830-11f0-b4ab-244bfe56a7b1
2025/06/29-02:58:54.396604 25b4 EVENT_LOG_v1 {"time_micros": 1751151534396593, "job": 1, "event": "recovery_started", "wal_files": [198]}
2025/06/29-02:58:54.396628 25b4 [db\db_impl\db_impl_open.cc:1131] Recovering log #198 mode 2
2025/06/29-02:58:54.435331 25b4 EVENT_LOG_v1 {"time_micros": 1751151534435284, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 202, "file_size": 1375, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 600, "largest_seqno": 613, "table_properties": {"data_size": 347, "index_size": 74, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 951, "raw_average_key_size": 67, "raw_value_size": 77, "raw_average_value_size": 5, "num_data_blocks": 1, "num_entries": 14, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "LZ4", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751151534, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "1ac3d8dc-4830-11f0-b4ab-244bfe56a7b1", "db_session_id": "AOHDQY4T2OB83BGOXDQM", "orig_file_number": 202, "seqno_to_time_mapping": "N/A"}}
2025/06/29-02:58:54.447155 25b4 EVENT_LOG_v1 {"time_micros": 1751151534447136, "job": 1, "event": "recovery_finished"}
2025/06/29-02:58:54.448202 25b4 [db\version_set.cc:5304] Creating manifest 204
2025/06/29-02:58:54.523567 25b4 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000198.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-02:58:54.523602 25b4 [db\db_impl\db_impl_files.cc:654] [JOB 2] Delete info log file E:\napoly\server-data\db\default//LOG.old.1750689694633909
2025/06/29-02:58:54.523848 25b4 [db\db_impl\db_impl_open.cc:2085] SstFileManager instance 00000240A75A2320
2025/06/29-02:58:54.525647 25b4 DB pointer 00000240A9A65040
2025/06/29-02:58:54.526311 6214 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-02:58:54.526331 6214 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    4.03 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.038       0      0       0.0       0.0
  L1      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.38 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.038       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.038       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.04              0.00         1    0.038       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000240A77093B0#17428 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 1 last_copies: 0 last_secs: 6.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
