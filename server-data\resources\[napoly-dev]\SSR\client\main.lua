local ESX = exports["es_extended"]:getSharedObject()
local isRestartInProgress = false
local countdownTime = 0
local lastTick = 0
local isInFinalMinute = false

local currentWeather = 'CLEAR'
local isWeatherTransitioning = false
local isThunderActive = false
local lastThunderTime = 0
local lastWeatherPhase = nil
local originalWeatherSync = true

-- معالج أحداث cd_easytime للتأكد من عدم التداخل
RegisterNetEvent('cd_easytime:SyncWeather')
AddEventHandler('cd_easytime:SyncWeather', function(data)
    -- إذا كان العد التنازلي نشط، تجاهل تحديثات cd_easytime
    if isRestartInProgress then
        if Config.Debug then
            print("SSR: تم تجاهل تحديث cd_easytime أثناء العد التنازلي")
        end
        return
    end
    -- تحديث الطقس الحالي إذا لم يكن هناك عد تنازلي
    if data and data.weather then
        currentWeather = data.weather
    end
end)

RegisterNetEvent('cd_easytime:ForceUpdate')
AddEventHandler('cd_easytime:ForceUpdate', function(data)
    -- إذا كان العد التنازلي نشط، تجاهل تحديثات cd_easytime
    if isRestartInProgress then
        if Config.Debug then
            print("SSR: تم تجاهل ForceUpdate من cd_easytime أثناء العد التنازلي")
        end
        return
    end
    -- تحديث الطقس الحالي إذا لم يكن هناك عد تنازلي
    if data and data.weather then
        currentWeather = data.weather
    end
end)

-- مستمع لأحداث تحذير التسونامي من cd_easytime
RegisterNetEvent('cd_easytime:StartTsunamiCountdown')
AddEventHandler('cd_easytime:StartTsunamiCountdown', function(tsunamiStarting)
    if tsunamiStarting then
        -- تشغيل العد التنازلي للتسونامي
        TriggerEvent('ssr:startCountdown', Config.DefaultRestartTime, true)
        -- تشغيل صفارة الإنذار
        TriggerServerEvent('InteractSound_SV:PlayWithinDistance', Config.TsunamiSiren.MaxDistance, Config.TsunamiSiren.SoundFile, Config.TsunamiSiren.Volume)
    end
end)

-- وظيفة محسنة للتحقق مما إذا كان اللاعب في الخارج
-- متغيرات للذاكرة المؤقتة
local lastLocationCheck = 0
local lastLocationResult = false
local locationCheckInterval = 1000 -- فحص كل ثانية
local currentTime = 0
local currentHour = 0
local currentMinute = 0

-- مزامنة الوقت مع cd_easytime
RegisterNetEvent('cd_easytime:UpdateTime')
AddEventHandler('cd_easytime:UpdateTime', function(hour, minute)
    currentHour = hour
    currentMinute = minute
    NetworkOverrideClockTime(hour, minute, 0)
    
    -- التحقق من الوقت للتأثيرات الخاصة
    local isDawn = (hour >= 5 and hour < 7)
    local isDusk = (hour >= 18 and hour < 20)
    
    if isDawn or isDusk then
        -- تطبيق تأثيرات خاصة للفجر والغسق
        local timecycleMod = isDawn and 'dawn' or 'dusk'
        SetTimecycleModifier(timecycleMod)
        SetTimecycleModifierStrength(0.3)
    else
        ClearTimecycleModifier()
    end
end)

-- مزامنة حالة الطقس
RegisterNetEvent('cd_easytime:SyncWeather')
AddEventHandler('cd_easytime:SyncWeather', function(data)
    if data.weather and data.weather ~= currentWeather then
        if not isWeatherTransitioning then
            isWeatherTransitioning = true
            
            -- تطبيق التحول التدريجي في الطقس
            SetWeatherTypeOvertimePersist(data.weather, 15.0)
            
            Citizen.Wait(15000)
            currentWeather = data.weather
            isWeatherTransitioning = false
            
            -- تحديث تأثيرات إضافية بناءً على الطقس
            if data.weather == 'RAIN' or data.weather == 'THUNDER' then
                SetRainLevel(Config.Weather.RainLevel)
            else
                SetRainLevel(0.0)
            end
        end
    end
end)

local function IsPlayerOutside()
    local currentTime = GetGameTimer()
    
    -- استخدام النتيجة المخزنة مؤقتًا إذا لم يمر وقت كافٍ
    if currentTime - lastLocationCheck < locationCheckInterval then
        return lastLocationResult
    end
    
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    
    local ray = StartExpensiveSynchronousShapeTestLosProbe(
        playerCoords.x, playerCoords.y, playerCoords.z + Config.LocationCheck.StartOffset,
        playerCoords.x, playerCoords.y, playerCoords.z + Config.LocationCheck.RayHeight,
        Config.LocationCheck.IgnorePlayer,
        Config.LocationCheck.IgnoreVehicles,
        Config.LocationCheck.RayType
    )
    
    local retval, hit, endCoords = GetShapeTestResult(ray)
    lastLocationResult = (hit == 0)
    lastLocationCheck = currentTime
    
    if Config.Debug then
        print("فحص الخارج - hit:", hit, "النتيجة:", lastLocationResult)
    end
    
    return lastLocationResult
end



-- مراقبة العاصفة وربطها مع cd_easytime
Citizen.CreateThread(function()
    while true do
        if isRestartInProgress and isInFinalMinute then
            -- تطبيق تأثيرات الطقس مباشرة
            ClearOverrideWeather()
            ClearWeatherTypePersist()
            SetWeatherTypePersist('THUNDER')
            SetWeatherTypeNow('THUNDER')
            SetWeatherTypeNowPersist('THUNDER')
            
            -- تطبيق تأثيرات إضافية
            SetRainLevel(Config.Weather.RainLevel)
            SetCloudHatOpacity(Config.Weather.CloudOpacity)
        end
        Citizen.Wait(1000)
    end
end)

-- وظيفة بدء تأثيرات الدقيقة الأخيرة
function StartFinalMinuteEffects()
    if isInFinalMinute then return end
    
    isInFinalMinute = true
    --print("بدء تأثيرات الدقيقة الأخيرة")
    
    -- تشغيل صوت التسونامي مرتين
    local sirenCount = 0
    local function playLastMinuteSiren()
        if sirenCount < 2 then
            TriggerServerEvent('InteractSound_SV:PlayOnAll', Config.TsunamiSiren.SoundFile, Config.TsunamiSiren.Volume)
            sirenCount = sirenCount + 1
            
            -- إيقاف الصوت الحالي وتشغيل الصوت التالي بعد المدة المحددة
            Citizen.SetTimeout(Config.TsunamiSiren.Duration, function()
                TriggerServerEvent('InteractSound_SV:Stop')
                if sirenCount < 2 then
                    -- انتظر 500 مللي ثانية قبل تشغيل الصوت التالي
                    Citizen.SetTimeout(500, playLastMinuteSiren)
                end
            end)
        end
    end
    
    -- بدء تشغيل الصوت
    playLastMinuteSiren()
    
    -- تهيئة العاصفة وربطها مع cd_easytime
    TriggerEvent('cd_easytime:ForceUpdate', {
        weather = 'THUNDER',
        instantweather = true,
        blackout = false
    })
    SetRainLevel(Config.Weather.RainLevel)
    SetCloudHatOpacity(Config.Weather.CloudOpacity)
    

end

-- وظيفة تحديث الطقس التدريجي
function UpdateProgressiveWeather()
    local currentPhase = nil

    -- تحديد المرحلة الحالية حسب التسلسل الجديد
    if countdownTime > Config.WeatherPhases.WINDY then
        currentPhase = 'CALM'
    elseif countdownTime > Config.WeatherPhases.HALLOWEEN then
        currentPhase = 'WINDY'
    elseif countdownTime > Config.WeatherPhases.PROGRESSIVE_RAIN then
        currentPhase = 'HALLOWEEN'
    elseif countdownTime > Config.WeatherPhases.THUNDER_STORM then
        currentPhase = 'PROGRESSIVE_RAIN'
    else
        currentPhase = 'THUNDER_STORM'
    end

    -- تطبيق التغيير فقط إذا تغيرت المرحلة
    if lastWeatherPhase ~= currentPhase then
        if Config.Debug then
            print("SSR: تغيير المرحلة من " .. (lastWeatherPhase or "لا شيء") .. " إلى " .. currentPhase)
        end
        lastWeatherPhase = currentPhase

        if currentPhase == 'CALM' then
            -- 5-4 دقائق: طقس عادي هادئ
            if Config.Debug then
                print("SSR: طقس عادي هادئ - الوقت المتبقي: " .. countdownTime)
            end
            -- إيقاف مزامنة cd_easytime مؤقتاً
            TriggerEvent('cd_easytime:PauseSync', true, 30)
            -- تطبيق طقس صافي مباشرة
            ChangeWeatherDirect('CLEAR', false, 20.0)
            SetRainLevel(0.0)
            SetCloudHatOpacity(0.1)
            SetWindSpeed(3.0)

        elseif currentPhase == 'WINDY' then
            -- الدقيقة 4: رياح قوية + غيوم قليلة
            if Config.Debug then
                print("SSR: رياح قوية مع غيوم قليلة - الوقت المتبقي: " .. countdownTime)
            end
            ChangeWeatherDirect('CLOUDS', true, 10.0)
            SetRainLevel(0.0)
            SetCloudHatOpacity(0.3)
            SetWindSpeed(20.0)
            SetWindDirection(math.random() * 360.0)

            -- تأكيد إضافي
            Citizen.Wait(300)
            SetWeatherTypePersist('CLOUDS')
            SetWeatherTypeNow('CLOUDS')

        elseif currentPhase == 'HALLOWEEN' then
            -- الدقيقة 3: طقس الهالوين + رياح قوية + غيوم كثيفة
            if Config.Debug then
                print("SSR: ===== بدء تطبيق طقس الهالوين =====")
                print("SSR: الوقت المتبقي: " .. countdownTime)
                print("SSR: الطقس الحالي قبل التغيير: " .. GetPrevWeatherTypeHashName())
            end

            -- فرض تطبيق طقس الهالوين بقوة
            ForceWeatherChange('HALLOWEEN')
            SetRainLevel(0.0)
            SetCloudHatOpacity(0.8)
            SetWindSpeed(22.0)
            SetWindDirection(math.random() * 360.0)

            if Config.Debug then
                Citizen.Wait(1000)
                print("SSR: الطقس بعد التغيير: " .. GetPrevWeatherTypeHashName())
                print("SSR: ===== انتهاء تطبيق طقس الهالوين =====")
            end

        elseif currentPhase == 'PROGRESSIVE_RAIN' then
            -- الدقيقة 2: إضافة مطر تدريجي فقط (بدون تغيير الطقس)
            if Config.Debug then
                print("SSR: إضافة مطر تدريجي - الوقت المتبقي: " .. countdownTime)
            end
            -- لا نغير نوع الطقس، نبقي على HALLOWEEN
            -- فقط نضيف المطر تدريجياً
            local rainIntensity = math.min(0.9, (120 - countdownTime) / 60 * 0.9)
            SetRainLevel(rainIntensity)
            -- نبقي على نفس إعدادات الغيوم والرياح من المرحلة السابقة
            if Config.Debug then
                print("SSR: شدة المطر الحالية: " .. string.format("%.2f", rainIntensity))
            end

        elseif currentPhase == 'THUNDER_STORM' then
            -- الدقيقة 1: العاصفة الرعدية الكاملة
            if Config.Debug then
                print("SSR: عاصفة رعدية قوية - الوقت المتبقي: " .. countdownTime)
            end
            ChangeWeatherDirect('THUNDER', true, 3.0)
            SetRainLevel(1.0)
            SetCloudHatOpacity(1.0)
            SetWindSpeed(25.0)
            SetWindDirection(math.random() * 360.0)

            -- تأكيد إضافي للعاصفة الرعدية
            Citizen.Wait(500)
            SetWeatherTypePersist('THUNDER')
            SetWeatherTypeNow('THUNDER')
            SetWeatherTypeNowPersist('THUNDER')

            -- تأثيرات إضافية للعاصفة
            SetForceVehicleTrails(true)
            SetForcePedFootstepsTracks(true)

            -- تفعيل الرعد والوميض في الدقيقة الأخيرة
            if not isThunderActive then
                isThunderActive = true
                StartThunderEffects()
                StartStormAtmosphere()
                if Config.Debug then
                    print("SSR: تم تفعيل تأثيرات الرعد والبرق والعاصفة")
                end
            end
        end
    end

    -- تحديث تدريجي للمطر في مرحلة المطر التدريجي
    if currentPhase == 'PROGRESSIVE_RAIN' then
        local rainIntensity = math.min(0.9, (120 - countdownTime) / 60 * 0.9)
        SetRainLevel(rainIntensity)

        if Config.Debug and countdownTime % 10 < 1 then -- طباعة كل 10 ثوان
            print("SSR: تحديث المطر التدريجي - الشدة: " .. string.format("%.2f", rainIntensity))
        end
    end
end

-- وظيفة تغيير الطقس مباشرة (مثل cd_easytime)
function ChangeWeatherDirect(weather, instant, changespeed)
    -- التحقق من تأثيرات الثلج (مثل cd_easytime)
    CheckSnowSync(weather)

    if instant then
        ClearOverrideWeather()
        ClearWeatherTypePersist()
        SetWeatherTypePersist(weather)
        SetWeatherTypeNow(weather)
        SetWeatherTypeNowPersist(weather)
    else
        ClearOverrideWeather()
        SetWeatherTypeOvertimePersist(weather, changespeed or 180.0)
    end

    if Config.Debug then
        print("SSR: تم تغيير الطقس إلى " .. weather .. " - فوري: " .. tostring(instant))
    end
end

-- وظيفة فرض تطبيق الطقس بقوة (لحل مشاكل التداخل)
function ForceWeatherChange(weather)
    if Config.Debug then
        print("SSR: بدء فرض تطبيق الطقس: " .. weather)
    end

    -- إيقاف مزامنة cd_easytime مؤقتاً
    TriggerEvent('cd_easytime:PauseSync', true, 10)

    -- إيقاف أي تأثيرات طقس سابقة
    ClearOverrideWeather()
    ClearWeatherTypePersist()

    -- تطبيق الطقس الجديد بقوة
    SetWeatherTypePersist(weather)
    SetWeatherTypeNow(weather)
    SetWeatherTypeNowPersist(weather)

    -- تأكيد إضافي بعد تأخير قصير
    Citizen.CreateThread(function()
        Citizen.Wait(500)
        SetWeatherTypePersist(weather)
        SetWeatherTypeNow(weather)

        Citizen.Wait(1000)
        SetWeatherTypePersist(weather)
        SetWeatherTypeNow(weather)

        if Config.Debug then
            print("SSR: تم فرض تطبيق الطقس: " .. weather)
            print("SSR: الطقس الحالي: " .. GetPrevWeatherTypeHashName())
        end
    end)
end

-- وظيفة التحقق من تأثيرات الثلج (مطابقة لـ cd_easytime)
function CheckSnowSync(NewWeather)
    if currentWeather == 'XMAS' then
        SetForceVehicleTrails(false)
        SetForcePedFootstepsTracks(false)
    elseif NewWeather == 'XMAS' then
        SetForceVehicleTrails(true)
        SetForcePedFootstepsTracks(true)
    end
    currentWeather = NewWeather
end

-- وظيفة تشغيل تأثيرات الرعد والوميض
function StartThunderEffects()
    if Config.Debug then
        print("SSR: بدء تشغيل تأثيرات الرعد والبرق")
    end

    Citizen.CreateThread(function()
        while isThunderActive and countdownTime > 0 do
            local currentTime = GetGameTimer()
            local randomInterval = math.random(Config.Thunder.MinInterval, Config.Thunder.MaxInterval)

            -- التحقق من الفترة الزمنية بين الرعود
            if currentTime - lastThunderTime >= randomInterval then
                if Config.Debug then
                    print("SSR: تشغيل رعد وبرق - الفترة: " .. randomInterval)
                end

                -- تأثير البرق المتعدد (وميض سريع)
                for i = 1, 3 do
                    SetTimecycleModifier(Config.LightingEffects.CycleName)
                    SetTimecycleModifierStrength(Config.Thunder.FlashIntensity)
                    Citizen.Wait(100)
                    ClearTimecycleModifier()
                    Citizen.Wait(50)
                end

                -- وميض قوي نهائي
                SetTimecycleModifier(Config.LightingEffects.CycleName)
                SetTimecycleModifierStrength(1.5)

                -- تشغيل صوت الرعد
                TriggerServerEvent('InteractSound_SV:PlayWithinDistance', Config.Thunder.MaxDistance, Config.Thunder.SoundFile, Config.Thunder.Volume)

                -- تطبيق تأثير الزلزال مع الرعد
                TriggerEarthquakeEffect()

                -- انتظار مدة الوميض الطويل
                Citizen.Wait(Config.LightingEffects.IntenseDuration)

                -- إزالة الوميض تدريجياً
                SetTimecycleModifierStrength(0.8)
                Citizen.Wait(200)
                SetTimecycleModifierStrength(0.4)
                Citizen.Wait(200)
                ClearTimecycleModifier()

                lastThunderTime = currentTime
            end

            Citizen.Wait(500) -- فحص أسرع للحصول على تأثيرات أكثر سلاسة
        end

        if Config.Debug then
            print("SSR: توقف تأثيرات الرعد والبرق")
        end
    end)
end

-- وظيفة تأثير الزلزال مع الرعد
function TriggerEarthquakeEffect()
    if not Config.Earthquake.Enabled then return end

    -- التحقق من أن اللاعب في الخارج إذا كان مطلوب
    if Config.Earthquake.OnlyOutdoors then
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)

        -- فحص بسيط للتأكد من أن اللاعب في الخارج
        local ray = StartExpensiveSynchronousShapeTestLosProbe(
            playerCoords.x, playerCoords.y, playerCoords.z + 0.1,
            playerCoords.x, playerCoords.y, playerCoords.z + 20.0,
            1, 1, 1
        )

        local retval, hit, endCoords = GetShapeTestResult(ray)
        local isOutside = (hit == 0)

        if not isOutside then
            if Config.Earthquake.Debug then
                print("SSR: تم تجاهل تأثير الزلزال - اللاعب في الداخل")
            end
            return
        end
    end

    local playerPed = PlayerPedId()

    -- اهتزاز الشاشة
    ShakeGameplayCam(Config.Earthquake.ShakeType, Config.Earthquake.ShakeIntensity)

    if Config.Earthquake.Debug then
        print("SSR: تم تطبيق اهتزاز الشاشة - الشدة: " .. Config.Earthquake.ShakeIntensity)
    end

    -- إيقاف الاهتزاز بعد المدة المحددة
    Citizen.SetTimeout(Config.Earthquake.ShakeDuration, function()
        StopGameplayCamShaking(true)
    end)

    -- تأثير التعثر العشوائي
    local stumbleRoll = math.random(1, 100)
    if stumbleRoll <= Config.Earthquake.StumbleChance then
        -- التأكد من أن اللاعب ليس في مركبة
        if not IsPedInAnyVehicle(playerPed, false) then
            -- تطبيق قوة دفع عشوائية للتعثر
            local forceX = math.random(-Config.Earthquake.StumbleForce, Config.Earthquake.StumbleForce) / 10
            local forceY = math.random(-Config.Earthquake.StumbleForce, Config.Earthquake.StumbleForce) / 10
            local forceZ = math.random(0, Config.Earthquake.StumbleForce) / 20

            -- تطبيق التعثر
            SetPedToRagdoll(playerPed, Config.Earthquake.StumbleDuration, Config.Earthquake.StumbleDuration, 0, 0, 0, 0)

            -- إضافة قوة دفع خفيفة
            ApplyForceToEntity(playerPed, 1, forceX, forceY, forceZ, 0.0, 0.0, 0.0, 0, false, true, true, false, true)

            if Config.Earthquake.Debug then
                print("SSR: تم تطبيق تأثير التعثر - القوة: " .. forceX .. ", " .. forceY .. ", " .. forceZ)
            end
        else
            if Config.Earthquake.Debug then
                print("SSR: تم تجاهل التعثر - اللاعب في مركبة")
            end
        end
    else
        if Config.Earthquake.Debug then
            print("SSR: لم يحدث تعثر - النسبة: " .. stumbleRoll .. "% (المطلوب: " .. Config.Earthquake.StumbleChance .. "%)")
        end
    end
end

-- وظيفة تأثيرات الأجواء العاصفة
function StartStormAtmosphere()
    Citizen.CreateThread(function()
        while isThunderActive and countdownTime > 0 do
            -- تغيير اتجاه الرياح بشكل عشوائي
            SetWindDirection(math.random() * 360.0)

            -- تأثيرات بصرية إضافية
            if math.random(1, 10) <= 3 then -- 30% احتمال
                SetTimecycleModifier("storm")
                SetTimecycleModifierStrength(0.3)
                Citizen.Wait(2000)
                ClearTimecycleModifier()
            end

            Citizen.Wait(3000)
        end
    end)
end

-- تحديث حدث بدء العد التنازلي
RegisterNetEvent('ssr:startCountdown')
AddEventHandler('ssr:startCountdown', function(time, isAuto)
    if isRestartInProgress then
        CleanupAllEffects()
    end
    
    isRestartInProgress = true
    countdownTime = time
    lastTick = GetGameTimer()
    isInFinalMinute = false

    -- حفظ حالة المزامنة الأصلية وإيقاف cd_easytime مؤقتاً
    if Config.CDEasyTime.Enabled and Config.CDEasyTime.PauseOnCountdown then
        originalWeatherSync = true
        TriggerEvent('cd_easytime:PauseSync', true, 30)

        if Config.Debug then
            print("SSR: تم إيقاف مزامنة cd_easytime مؤقتاً")
        end
    end

    if Config.Debug then
        print("SSR: بدء العد التنازلي - الوقت: " .. time .. " ثانية")
    end

    -- تهيئة الطقس الأولي بشكل تدريجي
    ChangeWeatherDirect('CLEAR', false, 10.0)
    SetRainLevel(0.0)
    SetCloudHatOpacity(0.0)
    
    SendNUIMessage({
        type = 'showTimer',
        time = math.floor(countdownTime),
        isAuto = isAuto
    })
    
    -- بدء مراقبة الطقس
    Citizen.CreateThread(function()
        while isRestartInProgress and countdownTime > 0 do
            local currentTick = GetGameTimer()
            local deltaTime = currentTick - lastTick
            
            if deltaTime >= 100 then
                countdownTime = countdownTime - (deltaTime / 1000)
                lastTick = currentTick
                
                -- تحديث العداد
                SendNUIMessage({
                    type = 'updateTimer',
                    time = math.max(0, math.floor(countdownTime)),
                    isAuto = isAuto
                })
                
                -- تحديث الطقس التدريجي
                UpdateProgressiveWeather()
            end
            
            Citizen.Wait(0)
        end
        
        if countdownTime <= 0 then
            SendNUIMessage({
                type = 'hideTimer'
            })
            CleanupAllEffects()
        end
    end)
end)

-- وظيفة تنظيف التأثيرات
function CleanupAllEffects()
    -- إيقاف تأثيرات الرعد والعاصفة
    isThunderActive = false
    lastThunderTime = 0
    lastWeatherPhase = nil

    isInFinalMinute = false

    -- تنظيف التأثيرات البصرية أولاً
    StopGameplayCamShaking(true)
    ClearTimecycleModifier()

    -- تنظيف الطقس تدريجياً
    SetRainLevel(0.0)
    SetCloudHatOpacity(0.0)
    SetWindSpeed(5.0)
    SetWindDirection(0.0)

    -- إيقاف تأثيرات العاصفة
    SetForceVehicleTrails(false)
    SetForcePedFootstepsTracks(false)

    -- تنظيف إعدادات الطقس
    ClearOverrideWeather()
    ClearWeatherTypePersist()

    -- إعادة تفعيل مزامنة cd_easytime إذا كانت مفعلة
    if Config.CDEasyTime.Enabled and Config.CDEasyTime.RestoreAfterCountdown then
        TriggerEvent('cd_easytime:PauseSync', false)

        -- انتظار قصير ثم طلب المزامنة
        Citizen.Wait(Config.CDEasyTime.SyncDelay)
        TriggerServerEvent('cd_easytime:SyncMe')

        if Config.Debug then
            print("SSR: تم إعادة تفعيل مزامنة cd_easytime")
        end
    end

    if Config.Debug then
        print("SSR: تم تنظيف جميع التأثيرات وإعادة تفعيل cd_easytime")
    end
end

-- إيقاف العد التنازلي
RegisterNetEvent('ssr:cancelRestart')
AddEventHandler('ssr:cancelRestart', function()
    isRestartInProgress = false
    countdownTime = 0
    CleanupAllEffects()
    
    SendNUIMessage({
        type = 'hideTimer'
    })
end)







-- إضافة في بداية الملف
math.randomseed(GetGameTimer())
































